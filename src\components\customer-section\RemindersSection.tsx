import React from 'react';
import { Clock, PlusCircle, ChevronDown, AlertCircle, CheckCircle, Eye, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { BadgeRenderer, DateFormatter } from './types';

interface RemindersSectionProps {
  expanded: boolean;
  onToggle: () => void;
  items: any[];
  loading: boolean;
  error: boolean;
  onOpenList: () => void;
  onOpenCreate: () => void;
  onOpenDetails?: (reminder: any) => void;
  onEdit?: (reminder: any) => void;
  renderReminderTypeBadge: BadgeRenderer;
  renderPriorityBadge: BadgeRenderer;
  renderStatusBadge: BadgeRenderer;
  timeAgo: DateFormatter;
  onCompleteReminder?: (reminderId: string) => void;
  onSnoozeReminder?: (reminderId: string) => void;
  overdueCount: number;
  upcomingCount: number;
}

const RemindersSection: React.FC<RemindersSectionProps> = ({
  expanded,
  onToggle,
  items,
  loading,
  error,
  onOpenList,
  onOpenCreate,
  onOpenDetails,
  onEdit,
  renderReminderTypeBadge,
  renderPriorityBadge,
  renderStatusBadge,
  timeAgo,
  onCompleteReminder,
  onSnoozeReminder,
  overdueCount,
  upcomingCount,
}) => {
  // Sort items to show overdue first, then by remind_at
  const sortedItems = React.useMemo(() => {
    return [...items].sort((a, b) => {
      if (a.is_overdue && !b.is_overdue) return -1;
      if (!a.is_overdue && b.is_overdue) return 1;
      if (a.is_due && !b.is_due) return -1;
      if (!a.is_due && b.is_due) return 1;
      return new Date(a.remind_at).getTime() - new Date(b.remind_at).getTime();
    });
  }, [items]);
  return (
    <div className="space-y-2">
      <div
        className="flex items-center justify-between cursor-pointer group transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-md"
        onClick={onToggle}
      >
        <div className="flex items-center text-sm font-medium">
          <div className="relative">
            <Clock className="h-4 w-4 mr-2 text-orange-500" />
            {overdueCount > 0 && (
              <div className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full animate-pulse" />
            )}
          </div>
          <span>Reminders</span>
          <Badge variant="secondary" className="ml-2 text-xs">
            {items.length}
          </Badge>
          {overdueCount > 0 && (
            <Badge variant="destructive" className="ml-1 text-xs">
              {overdueCount} overdue
            </Badge>
          )}
          {upcomingCount > 0 && (
            <Badge variant="outline" className="ml-1 text-xs border-blue-200 text-blue-700">
              {upcomingCount} due soon
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onOpenCreate();
            }}
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <PlusCircle className="h-3 w-3" />
          </Button>
          <ChevronDown
            className={`h-4 w-4 transition-transform ${expanded ? "transform rotate-180" : ""
              }`}
          />
        </div>
      </div>

      {expanded && (
        <div className="space-y-2 pl-6">
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <SpinnerTemp />
            </div>
          ) : error ? (
            <div className="text-red-500 text-sm py-2">
              Failed to load reminders
            </div>
          ) : sortedItems.length === 0 ? (
            <div className="text-gray-500 text-sm py-2">No reminders found</div>
          ) : (
            <>
              {sortedItems.slice(0, 2).map((reminder) => (
                <div
                  key={reminder.reminder_id}
                  className={`p-3 border rounded-lg transition-all duration-200 hover:shadow-sm ${reminder.is_overdue
                    ? 'border-red-200 bg-red-50'
                    : reminder.is_due
                      ? 'border-yellow-200 bg-yellow-50'
                      : 'border-gray-200 bg-white'
                    }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        {renderReminderTypeBadge(reminder.reminder_type)}
                        {renderPriorityBadge(reminder.priority)}
                        {renderStatusBadge(reminder.status)}
                        {reminder.is_overdue && (
                          <AlertCircle className="h-3 w-3 text-red-500" />
                        )}
                        {reminder.is_due && !reminder.is_overdue && (
                          <Clock className="h-3 w-3 text-yellow-500" />
                        )}
                      </div>
                      <p className="font-medium text-sm truncate">
                        {reminder.title}
                      </p>
                      <p className="text-xs text-gray-600 mt-1">
                        Due: {timeAgo(reminder.remind_at)}
                      </p>
                      {reminder.tags && (
                        <p className="text-xs text-gray-500 mt-1">
                          Tags: {reminder.tags}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 hover:bg-gray-100"
                        onClick={() => onOpenDetails && onOpenDetails(reminder)}
                        title="View Details"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>

                      {onEdit && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 hover:bg-gray-100"
                          onClick={() => onEdit(reminder)}
                          title="Edit Reminder"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      )}

                      {reminder.status === 'Active' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onCompleteReminder && onCompleteReminder(reminder.reminder_id)}
                            className="h-6 w-6 p-0 text-green-600 hover:text-green-700"
                            title="Complete (Not Available)"
                            disabled={!onCompleteReminder}
                          >
                            <CheckCircle className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onSnoozeReminder && onSnoozeReminder(reminder.reminder_id)}
                            className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700"
                            title="Snooze (Not Available)"
                            disabled={!onSnoozeReminder}
                          >
                            <Clock className="h-3 w-3" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {sortedItems.length > 2 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onOpenList}
                  className="w-full text-xs text-gray-600 hover:text-gray-800"
                >
                  View all reminders ({sortedItems.length})
                </Button>
              )}
            </>
          )}

          <Button
            variant="outline"
            size="sm"
            className="w-full text-xs bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 flex gap-1"
            onClick={onOpenCreate}
          >
            <PlusCircle className="h-3 w-3" />
            <span>Add reminder</span>
          </Button>
        </div>
      )}
    </div>
  );
};

export default RemindersSection;