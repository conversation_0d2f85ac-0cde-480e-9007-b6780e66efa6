import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetT<PERSON>le, SheetTrigger } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Calendar,
  Clock,
  User,
  Phone,
  Mail,
  MessageSquare,
  Users,
  MapPin,
  Star,
  ArrowRight,
  Edit2,
  Trash2,
  Play,
  Square,
  CheckCircle
} from "lucide-react";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import Multiselect from "@/components/custom/forms/Multiselect";
import { useGetUsersQuery } from "@/redux/slices/user";
import { formatDate } from "@/utils/formatDate";

// Import the EngagementFormData interface from types.ts instead of defining it here
import { EngagementFormData } from "./types";

interface EngagementsModalsProps {
  activeModal: string | null;
  onCloseModal: () => void;
  onOpenModal: (modalName: string) => void;
  engagementForm: EngagementFormData;
  onEngagementFormChange: (field: keyof EngagementFormData, value: string | boolean | number) => void;
  formErrors: Record<string, string>;
  successMessage: string;
  editingEngagement: any;
  onOpenEditEngagement: (engagement: any) => void;
  onCreateEngagement: () => void;
  onUpdateEngagement: () => void;
  onDeleteEngagement: (engagementId: string, createdBy: string) => void;
  engagementItems: any[];
  currentUser: any;
  isCreating: boolean;
  isDeleting: boolean;
  renderEngagementTypeBadge: (type: string) => JSX.Element;
  renderEngagementStatusBadge: (status: string) => JSX.Element;
  formatDate: (dateString: string) => string;
}

const EngagementsModals: React.FC<EngagementsModalsProps> = ({
  activeModal,
  onCloseModal,
  onOpenModal,
  engagementForm,
  onEngagementFormChange,
  formErrors,
  successMessage,
  editingEngagement,
  onOpenEditEngagement,
  onCreateEngagement,
  onUpdateEngagement,
  onDeleteEngagement,
  engagementItems,
  currentUser,
  isCreating,
  isDeleting,
  renderEngagementTypeBadge,
  renderEngagementStatusBadge,
  formatDate,
}) => {

  // User fetching state
  const { data: usersData, isLoading: usersLoading } = useGetUsersQuery({});
  const [userOptions, setUserOptions] = useState<{ value: string; label: string }[]>([]);
  const [selectedUser, setSelectedUser] = useState<{ label: string; value: string } | null>(null);

  // Process users data for the multiselect
  useEffect(() => {
    if (usersData?.data?.results && usersData.data.results.length > 0) {
      const options = usersData.data.results.map((user: any) => ({
        label: `${user?.fullnames} (${user?.employee_no})`,
        value: user?.employee_no,
      }));
      setUserOptions(options);
    }
  }, [usersData]);

  // Set selected user when editing an engagement
  useEffect(() => {
    if (editingEngagement && activeModal === "editEngagement" && userOptions.length > 0) {
      const assignedUser = userOptions.find(option => option.value === engagementForm.assigned_to);
      setSelectedUser(assignedUser || null);
    }
  }, [editingEngagement, engagementForm.assigned_to, userOptions, activeModal]);

  // Handle user selection
  const handleUserSelection = (user: { label: string; value: string } | null) => {
    setSelectedUser(user);
    onEngagementFormChange('assigned_to', user?.value || '');
  };

  // Reset selected user when modal closes or changes
  useEffect(() => {
    if (!activeModal || activeModal === "engagements") {
      setSelectedUser(null);
    }
  }, [activeModal]);

  const getEngagementTypeIcon = (type: string) => {
    const icons = {
      Call: Phone,
      Email: Mail,
      Meeting: Users,
      SMS: MessageSquare,
      Chat: MessageSquare,
      Visit: MapPin,
      Event: Star,
      "Follow-up": ArrowRight,
    };
    const IconComponent = icons[type as keyof typeof icons] || Calendar;
    return <IconComponent className="h-4 w-4" />;
  };

  const formatDateTime = (dateTimeString: string) => {
    if (!dateTimeString) return "N/A";
    const date = new Date(dateTimeString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canEditEngagement = (engagement: any) => {
    // Check multiple possible field names for created_by
    const createdBy = engagement.created_by || engagement.created_by_id || engagement.createdBy;

    // TEMPORARY FIX: Since API doesn't return creator info, allow all users to edit
    // TODO: Fix API to return creator information for proper permission checks
    if (!createdBy) {
      return true; // Allow all users to edit when creator info is missing
    }

    return currentUser?.id === createdBy;
  };

  const canDeleteEngagement = (engagement: any) => {
    // Check multiple possible field names for created_by
    const createdBy = engagement.created_by || engagement.created_by_id || engagement.createdBy;

    // TEMPORARY FIX: Since API doesn't return creator info, allow all users to delete
    // TODO: Fix API to return creator information for proper permission checks
    if (!createdBy) {
      return true; // Allow all users to delete when creator info is missing
    }

    return currentUser?.id === createdBy;
  };

  return (
    <>
      {/* Engagements List Modal */}
      <Sheet open={activeModal === "engagements"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[600px] sm:max-w-[600px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              All Engagements
            </SheetTitle>
            <SheetDescription>
              View and manage all customer engagements
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-between items-center my-4">
            <p className="text-sm text-muted-foreground">
              {engagementItems.length} engagement{engagementItems.length !== 1 ? 's' : ''} found
            </p>
            <Button onClick={() => onOpenModal("createEngagement")} size="sm">
              Add New Engagement
            </Button>
          </div>

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4">
              {engagementItems.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center text-muted-foreground">
                      <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No engagements found</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => onOpenModal("createEngagement")}
                      >
                        Create First Engagement
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                engagementItems.map((engagement) => (
                  <Card key={engagement.engagement_id} className="relative">
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {getEngagementTypeIcon(engagement.engagement_type)}
                          <div>
                            <CardTitle className="text-sm">{engagement.subject}</CardTitle>
                            <CardDescription className="text-xs">
                              {engagement.engagement_type} • {formatDateTime(engagement.created_at)}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {renderEngagementTypeBadge(engagement.engagement_type)}
                          <Badge variant="outline" className="text-xs">
                            {engagement.client_type}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                        {engagement.description}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          {engagement.created_by && (
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {engagement.created_by}
                            </span>
                          )}
                          {engagement.follow_up_required && engagement.follow_up_date && (
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Follow-up: {formatDate(engagement.follow_up_date)}
                            </span>
                          )}
                          {engagement.set_reminder && engagement.reminder_time && (
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Reminder: {formatDate(engagement.reminder_time)}
                            </span>
                          )}
                        </div>

                        <div className="flex items-center gap-2">
                          {/* Management Buttons */}
                          <div className="flex items-center gap-1 border-l pl-2 ml-1">
                            {canEditEngagement(engagement) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onOpenEditEngagement(engagement)}
                                className="h-7 w-7 p-0 hover:bg-gray-100"
                                title="Edit Engagement"
                              >
                                <Edit2 className="h-3 w-3" />
                              </Button>
                            )}

                            {canDeleteEngagement(engagement) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onDeleteEngagement(engagement.engagement_id, engagement.created_by || engagement.created_by_id || currentUser?.id || "")}
                                className="h-7 w-7 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                disabled={isDeleting}
                                title="Delete Engagement"
                              >
                                {isDeleting ? (
                                  <SpinnerTemp />
                                ) : (
                                  <Trash2 className="h-3 w-3" />
                                )}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>

                      {engagement.outcome && (
                        <div className="mt-2 pt-2 border-t">
                          <p className="text-xs text-muted-foreground">
                            <strong>Outcome:</strong> {engagement.outcome}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {/* Create Engagement Modal */}
      <Sheet open={activeModal === "createEngagement"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[500px] sm:max-w-[500px]">
          <SheetHeader>
            <SheetTitle>Create New Engagement</SheetTitle>
            <SheetDescription>
              Schedule a new customer engagement
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          {formErrors.submit && (
            <Alert className="bg-red-50 border-red-200 text-red-800 mb-4">
              <AlertDescription>{formErrors.submit}</AlertDescription>
            </Alert>
          )}

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="engagement_type">Engagement Type *</Label>
                  <Select
                    value={engagementForm.engagement_type}
                    onValueChange={(value) => onEngagementFormChange('engagement_type', value)}
                  >
                    <SelectTrigger className={formErrors.engagement_type ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Call">Call</SelectItem>
                      <SelectItem value="Email">Email</SelectItem>
                      <SelectItem value="Meeting">Meeting</SelectItem>
                      <SelectItem value="SMS">SMS</SelectItem>
                      <SelectItem value="Chat">Chat</SelectItem>
                      <SelectItem value="Visit">Visit</SelectItem>
                      <SelectItem value="Event">Event</SelectItem>
                      <SelectItem value="Follow-up">Follow-up</SelectItem>
                      <SelectItem value="Contact-add">Contact-add</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.engagement_type && (
                    <p className="text-sm text-red-600">{formErrors.engagement_type}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="set_reminder"
                      checked={engagementForm.set_reminder}
                      onCheckedChange={(checked) => onEngagementFormChange('set_reminder', checked as boolean)}
                    />
                    <Label htmlFor="set_reminder">Set Reminder</Label>
                  </div>
                  {engagementForm.set_reminder && (
                    <div className="space-y-2">
                      <Label htmlFor="reminder_time">Reminder Time</Label>
                      <Input
                        id="reminder_time"
                        type="datetime-local"
                        value={engagementForm.reminder_time}
                        onChange={(e) => onEngagementFormChange('reminder_time', e.target.value)}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Subject *</Label>
                <Input
                  id="subject"
                  value={engagementForm.subject}
                  onChange={(e) => onEngagementFormChange('subject', e.target.value)}
                  placeholder="Enter engagement subject"
                  className={formErrors.subject ? "border-red-500" : ""}
                />
                {formErrors.subject && (
                  <p className="text-sm text-red-600">{formErrors.subject}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={engagementForm.description}
                  onChange={(e) => onEngagementFormChange('description', e.target.value)}
                  placeholder="Enter engagement description"
                  rows={3}
                  className={formErrors.description ? "border-red-500" : ""}
                />
                {formErrors.description && (
                  <p className="text-sm text-red-600">{formErrors.description}</p>
                )}
              </div>





              <Separator />

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="follow_up_required"
                    checked={engagementForm.follow_up_required}
                    onCheckedChange={(checked) => onEngagementFormChange('follow_up_required', checked as boolean)}
                  />
                  <Label htmlFor="follow_up_required">Follow-up Required</Label>
                </div>

                {engagementForm.follow_up_required && (
                  <div className="space-y-2">
                    <Label htmlFor="follow_up_date">Follow-up Date</Label>
                    <Input
                      id="follow_up_date"
                      type="date"
                      value={engagementForm.follow_up_date}
                      onChange={(e) => onEngagementFormChange('follow_up_date', e.target.value)}
                    />
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onCloseModal}>
              Cancel
            </Button>
            <Button onClick={onCreateEngagement} disabled={isCreating}>
              {isCreating ? (
                <>
                  <SpinnerTemp />
                  Creating...
                </>
              ) : (
                'Create Engagement'
              )}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Edit Engagement Modal */}
      <Sheet open={activeModal === "editEngagement"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[500px] sm:max-w-[500px]">
          <SheetHeader>
            <SheetTitle>Edit Engagement</SheetTitle>
            <SheetDescription>
              Update engagement details
            </SheetDescription>
          </SheetHeader>

          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800 mb-4">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          {formErrors.submit && (
            <Alert className="bg-red-50 border-red-200 text-red-800 mb-4">
              <AlertDescription>{formErrors.submit}</AlertDescription>
            </Alert>
          )}

          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_engagement_type">Engagement Type *</Label>
                  <Select
                    value={engagementForm.engagement_type}
                    onValueChange={(value) => onEngagementFormChange('engagement_type', value)}
                  >
                    <SelectTrigger className={formErrors.engagement_type ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Call">Call</SelectItem>
                      <SelectItem value="Email">Email</SelectItem>
                      <SelectItem value="Meeting">Meeting</SelectItem>
                      <SelectItem value="SMS">SMS</SelectItem>
                      <SelectItem value="Chat">Chat</SelectItem>
                      <SelectItem value="Visit">Visit</SelectItem>
                      <SelectItem value="Event">Event</SelectItem>
                      <SelectItem value="Follow-up">Follow-up</SelectItem>
                      <SelectItem value="Contact-add">Contact-add</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.engagement_type && (
                    <p className="text-sm text-red-600">{formErrors.engagement_type}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="edit_set_reminder"
                      checked={engagementForm.set_reminder}
                      onCheckedChange={(checked) => onEngagementFormChange('set_reminder', checked as boolean)}
                    />
                    <Label htmlFor="edit_set_reminder">Set Reminder</Label>
                  </div>
                  {engagementForm.set_reminder && (
                    <div className="space-y-2">
                      <Label htmlFor="edit_reminder_time">Reminder Time</Label>
                      <Input
                        id="edit_reminder_time"
                        type="datetime-local"
                        value={engagementForm.reminder_time}
                        onChange={(e) => onEngagementFormChange('reminder_time', e.target.value)}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_subject">Subject *</Label>
                <Input
                  id="edit_subject"
                  value={engagementForm.subject}
                  onChange={(e) => onEngagementFormChange('subject', e.target.value)}
                  placeholder="Enter engagement subject"
                  className={formErrors.subject ? "border-red-500" : ""}
                />
                {formErrors.subject && (
                  <p className="text-sm text-red-600">{formErrors.subject}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_description">Description *</Label>
                <Textarea
                  id="edit_description"
                  value={engagementForm.description}
                  onChange={(e) => onEngagementFormChange('description', e.target.value)}
                  placeholder="Enter engagement description"
                  rows={3}
                  className={formErrors.description ? "border-red-500" : ""}
                />
                {formErrors.description && (
                  <p className="text-sm text-red-600">{formErrors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_scheduled_at">Scheduled Date & Time *</Label>
                  <Input
                    id="edit_scheduled_at"
                    type="datetime-local"
                    value={engagementForm.scheduled_at}
                    onChange={(e) => onEngagementFormChange('scheduled_at', e.target.value)}
                    className={formErrors.scheduled_at ? "border-red-500" : ""}
                  />
                  {formErrors.scheduled_at && (
                    <p className="text-sm text-red-600">{formErrors.scheduled_at}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_duration">Duration (minutes)</Label>
                  <Input
                    id="edit_duration"
                    type="number"
                    value={engagementForm.duration_minutes || ''}
                    onChange={(e) => onEngagementFormChange('duration_minutes', e.target.value ? parseInt(e.target.value) : 0)}
                    placeholder="Duration in minutes"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Assigned To</Label>
                <Multiselect
                  value={selectedUser}
                  data={userOptions}
                  setValue={handleUserSelection}
                  loading={usersLoading}
                  isClearable={true}
                  isDisabled={false}
                  isMultiple={false}
                  isSearchable={true}
                />
                {formErrors.assigned_to && (
                  <p className="text-sm text-red-600">{formErrors.assigned_to}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_outcome">Outcome</Label>
                <Select
                  value={engagementForm.outcome}
                  onValueChange={(value) => onEngagementFormChange('outcome', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select outcome" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Successful">Successful</SelectItem>
                    <SelectItem value="Unsuccessful">Unsuccessful</SelectItem>
                    <SelectItem value="Partial">Partial</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit_notes">Notes</Label>
                <Textarea
                  id="edit_notes"
                  value={engagementForm.notes}
                  onChange={(e) => onEngagementFormChange('notes', e.target.value)}
                  placeholder="Additional notes"
                  rows={2}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit_follow_up_required"
                    checked={engagementForm.follow_up_required}
                    onCheckedChange={(checked) => onEngagementFormChange('follow_up_required', checked as boolean)}
                  />
                  <Label htmlFor="edit_follow_up_required">Follow-up Required</Label>
                </div>

                {engagementForm.follow_up_required && (
                  <div className="space-y-2">
                    <Label htmlFor="edit_follow_up_date">Follow-up Date</Label>
                    <Input
                      id="edit_follow_up_date"
                      type="date"
                      value={engagementForm.follow_up_date}
                      onChange={(e) => onEngagementFormChange('follow_up_date', e.target.value)}
                    />
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onCloseModal}>
              Cancel
            </Button>
            <Button onClick={onUpdateEngagement} disabled={isCreating}>
              {isCreating ? (
                <>
                  <SpinnerTemp />
                  Updating...
                </>
              ) : (
                'Update Engagement'
              )}
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* View Engagement Modal */}
      <Sheet open={activeModal === 'viewEngagement'} onOpenChange={onCloseModal}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle>Engagement Details</SheetTitle>
            <SheetDescription>
              View engagement information
            </SheetDescription>
          </SheetHeader>

          {editingEngagement && (
            <div className="space-y-6 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Type:</label>
                  <div className="mt-1">
                    {renderEngagementTypeBadge(editingEngagement.engagement_type)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Client Type:</label>
                  <div className="mt-1">
                    <Badge variant="outline" className="text-xs">
                      {editingEngagement.client_type}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Subject:</label>
                <p className="mt-1 text-sm text-gray-900">{editingEngagement.subject}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Description:</label>
                <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{editingEngagement.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Follow-up Required:</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {editingEngagement.follow_up_required ? 'Yes' : 'No'}
                  </p>
                </div>
                {editingEngagement.follow_up_date && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Follow-up Date:</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatDate(editingEngagement.follow_up_date)}
                    </p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Reminder Set:</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {editingEngagement.set_reminder ? 'Yes' : 'No'}
                  </p>
                </div>
                {editingEngagement.reminder_time && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Reminder Time:</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatDate(editingEngagement.reminder_time)}
                    </p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Created By:</label>
                  <p className="mt-1 text-sm text-gray-900">{editingEngagement.created_by || 'Unknown'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Created At:</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatDate(editingEngagement.created_at)}
                  </p>
                </div>
              </div>

              {/* Entity Information */}
              <div>
                <label className="text-sm font-medium text-gray-700">Related To:</label>
                <p className="mt-1 text-sm text-gray-900">
                  {editingEngagement.customer && `Customer: ${editingEngagement.customer}`}
                  {editingEngagement.prospect && `Prospect: ${editingEngagement.prospect}`}
                  {editingEngagement.sale && `Sale: ${editingEngagement.sale}`}
                </p>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onCloseModal}>
              Close
            </Button>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
};

export default EngagementsModals;