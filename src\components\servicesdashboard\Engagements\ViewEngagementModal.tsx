import { useState, useEffect } from 'react';
import MultiStepModal from '@/components/custom/modals/MultiStepModal';
import { Label } from '@/components/ui/label';
import {
  Users,
  Calendar,
  User,
  Building2,
  FileText,
  Paperclip,
  Star,
  Eye,
  Edit3,
  Save,
  X,
  CheckCircle,
  Clock,
  MessageSquare,
  Heart,
  Phone,
  Mail,
  Users as UsersIcon,
  Building2 as VisitIcon,
  Calendar as EventIcon,
  UserPlus,
  Target,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Badge } from '@/components/ui/badge';
import { LucideIcon } from 'lucide-react';

interface Participant {
  id: string;
  avatar: string;
  name: string;
  role: string;
}

interface Comment {
  id: string;
  text: string;
}

interface EngagementData {
  id: string;
  unread: boolean;
  engagement_type: 'Call' | 'Email' | 'Meeting' | 'SMS' | 'Chat' | 'Visit' | 'Event' | 'Follow-up' | 'Contact-add';
  title: string;
  customer: string | null;
  prospect: number | null;
  sale: string | null;
  marketingTeam: string;
  createdDate: string;
  starred: boolean;
  participants: Participant[];
  comments: Comment[];
  attachment: File | null;
  description: string;
}

interface ViewEngagementModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  engagementData: EngagementData | null;
  client_type: 'Customer' | 'Prospect' | 'Sale';
  onSubmit: (
    data: Omit<EngagementData, 'id' | 'createdDate' | 'participants' | 'comments'> & {
      client_type: 'Customer' | 'Prospect' | 'Sale';
    }
  ) => void;
}

export default function ViewEngagementModal({
  isOpen,
  onOpenChange,
  engagementData,
  client_type,
  onSubmit,
}: ViewEngagementModalProps) {
  const initialFormData = {
    title: '',
    engagement_type: '' as 'Call' | 'Email' | 'Meeting' | 'SMS' | 'Chat' | 'Visit' | 'Event' | 'Follow-up' | 'Contact-add' | '',
    customer: null as string | null,
    prospect: null as number | null,
    sale: null as string | null,
    client_type: client_type,
    marketingTeam: '',
    description: '',
    attachment: null as File | null,
    unread: false,
    starred: false,
  };

  const [currentStep, setCurrentStep] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState(initialFormData);

  useEffect(() => {
    if (engagementData) {
      setFormData({
        title: engagementData.title || '',
        engagement_type: engagementData.engagement_type || '',
        customer: engagementData.customer ?? null,
        prospect: engagementData.prospect ?? null,
        sale: engagementData.sale ?? null,
        client_type: client_type,
        marketingTeam: engagementData.marketingTeam || '',
        description: engagementData.description || '',
        attachment: engagementData.attachment ?? null,
        unread: engagementData.unread || false,
        starred: engagementData.starred || false,
      });
    } else {
      setFormData(initialFormData);
    }
  }, [engagementData, client_type]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: id === 'prospect' ? (value ? Number(value) : null) : value || null,
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData((prev) => ({ ...prev, attachment: file }));
  };

  const handleEngagementTypeChange = (
    value: 'Call' | 'Email' | 'Meeting' | 'SMS' | 'Chat' | 'Visit' | 'Event' | 'Follow-up' | 'Contact-add'
  ) => {
    setFormData((prev) => ({ ...prev, engagement_type: value }));
  };

  const handleClientTypeChange = (value: 'Customer' | 'Prospect' | 'Sale') => {
    setFormData((prev) => ({
      ...prev,
      client_type: value,
      customer: value === 'Customer' ? prev.customer : null,
      prospect: value === 'Prospect' ? prev.prospect : null,
      sale: value === 'Sale' ? prev.sale : null,
    }));
  };

  const handleSaveChanges = () => {
    if (engagementData) {
      onSubmit({ ...formData });
    }
    onOpenChange(false);
    setCurrentStep(0);
    setIsEditMode(false);
  };

  const getEngagementTypeIcon = (type: string): LucideIcon => {
    switch (type.toLowerCase()) {
      case 'call':
        return Phone;
      case 'email':
        return Mail;
      case 'meeting':
        return UsersIcon;
      case 'sms':
        return MessageSquare;
      case 'chat':
        return MessageSquare;
      case 'visit':
        return VisitIcon;
      case 'event':
        return EventIcon;
      case 'follow-up':
        return Clock;
      case 'contact-add':
        return UserPlus;
      default:
        return Building2;
    }
  };

  const getEngagementTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'call':
        return 'bg-blue-100 text-blue-700';
      case 'email':
        return 'bg-green-100 text-green-700';
      case 'meeting':
        return 'bg-purple-100 text-purple-700';
      case 'sms':
        return 'bg-teal-100 text-teal-700';
      case 'chat':
        return 'bg-cyan-100 text-cyan-700';
      case 'visit':
        return 'bg-orange-100 text-orange-700';
      case 'event':
        return 'bg-pink-100 text-pink-700';
      case 'follow-up':
        return 'bg-amber-100 text-amber-700';
      case 'contact-add':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getIdentifierValue = () => {
    if (!engagementData) {
      return 'No engagement data available';
    }
    switch (client_type) {
      case 'Customer':
        return engagementData.customer || 'No customer specified';
      case 'Prospect':
        return engagementData.prospect != null ? engagementData.prospect.toString() : 'No prospect number specified';
      case 'Sale':
        return engagementData.sale || 'No lead specified';
      default:
        return 'No identifier specified';
    }
  };

  const getFormIdentifierValue = () => {
    switch (formData.client_type) {
      case 'Customer':
        return formData.customer != null ? formData.customer : '';
      case 'Prospect':
        return formData.prospect != null ? formData.prospect.toString() : '';
      case 'Sale':
        return formData.sale != null ? formData.sale : '';
      default:
        return '';
    }
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={(open) => {
        onOpenChange(open);
        if (!open) {
          setCurrentStep(0);
          setIsEditMode(false);
        }
      }}
      title={isEditMode ? 'Edit Engagement Details' : 'View Engagement Details'}
      description={isEditMode ? 'Update the engagement details' : 'Review the details of the selected engagement'}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={() => {
        onOpenChange(false);
        setCurrentStep(0);
        setIsEditMode(false);
      }}
      steps={[
        {
          title: 'Basic Information',
          content: (
            <div className="space-y-6 py-4">
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  <Eye className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">
                  {isEditMode ? 'Edit Basic Details' : 'Engagement Overview'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {isEditMode ? 'Update the core engagement information' : 'Review the essential engagement details'}
                </p>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
                <div className="space-y-6">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-green-600" />
                      <Label htmlFor="title" className="text-sm font-medium text-gray-700">
                        Engagement Title {isEditMode && <span className="text-red-500">*</span>}
                      </Label>
                    </div>
                    {isEditMode ? (
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Enter a descriptive engagement title..."
                        className="border-2 border-green-200 focus:border-green-500 focus:ring-green-500 rounded-xl bg-white"
                      />
                    ) : (
                      <div className="bg-white rounded-xl p-4 border border-green-200 shadow-sm">
                        <p className="text-gray-800 font-medium">{engagementData?.title || 'No title specified'}</p>
                      </div>
                    )}
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-green-600" />
                      <Label className="text-sm font-medium text-gray-700">
                        Engagement Type {isEditMode && <span className="text-red-500">*</span>}
                      </Label>
                    </div>
                    {isEditMode ? (
                      <RadioGroup
                        value={formData.engagement_type}
                        onValueChange={handleEngagementTypeChange}
                        className="grid grid-cols-2 md:grid-cols-3 gap-3"
                      >
                        {[
                          'Call',
                          'Email',
                          'Meeting',
                          'SMS',
                          'Chat',
                          'Visit',
                          'Event',
                          'Follow-up',
                          'Contact-add',
                        ].map((type) => {
                          const Icon = getEngagementTypeIcon(type);
                          return (
                            <div key={type} className="flex items-center space-x-3">
                              <RadioGroupItem value={type} id={type.toLowerCase()} />
                              <Label
                                htmlFor={type.toLowerCase()}
                                className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                                  formData.engagement_type === type
                                    ? getEngagementTypeColor(type)
                                    : 'bg-white border-gray-200 text-gray-600'
                                }`}
                              >
                                <Icon className="h-4 w-4" />
                                <span className="text-sm font-medium">{type}</span>
                              </Label>
                            </div>
                          );
                        })}
                      </RadioGroup>
                    ) : (
                      <div className="bg-white rounded-xl p-4 border border-green-200 shadow-sm">
                        <Badge className={`${getEngagementTypeColor(engagementData?.engagement_type || '')} font-medium`}>
                          {(() => {
                            const Icon = getEngagementTypeIcon(engagementData?.engagement_type || '');
                            return <Icon className="h-3 w-3 mr-1" />;
                          })()}
                          {engagementData?.engagement_type || 'No type specified'}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ),
        },
        {
          title: 'Detailed Information',
          content: (
            <div className="space-y-6 py-4">
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  <MessageSquare className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">
                  {isEditMode ? 'Edit Engagement Details' : 'Engagement Details'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {isEditMode ? 'Update the detailed engagement information' : 'Review comprehensive engagement information'}
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-blue-600" />
                    <Label
                      htmlFor={formData.client_type === 'Prospect' ? 'prospect' : formData.client_type === 'Sale' ? 'sale' : 'customer'}
                      className="text-sm font-medium text-gray-700"
                    >
                      {client_type === 'Customer' ? 'Customer' : client_type === 'Prospect' ? 'Prospect No' : 'Lead No'}{' '}
                      {isEditMode && <span className="text-red-500">*</span>}
                    </Label>
                  </div>
                  {isEditMode ? (
                    <>
                      <Input
                        id={formData.client_type === 'Prospect' ? 'prospect' : formData.client_type === 'Sale' ? 'sale' : 'customer'}
                        value={getFormIdentifierValue()}
                        onChange={handleInputChange}
                        placeholder={`Enter ${
                          formData.client_type === 'Customer'
                            ? 'customer name'
                            : formData.client_type === 'Prospect'
                            ? 'prospect number'
                            : 'lead number'
                        }...`}
                        type={formData.client_type === 'Prospect' ? 'number' : 'text'}
                        className="border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-white"
                      />
                      <RadioGroup
                        value={formData.client_type}
                        onValueChange={handleClientTypeChange}
                        className="grid grid-cols-3 gap-3 mt-2"
                      >
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem value="Customer" id="customer-type" />
                          <Label
                            htmlFor="customer-type"
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                              formData.client_type === 'Customer'
                                ? 'bg-blue-50 border-blue-200 text-blue-700'
                                : 'bg-white border-gray-200 text-gray-600'
                            }`}
                          >
                            <User className="h-4 w-4" />
                            <span className="text-sm font-medium">Customer</span>
                          </Label>
                        </div>
                        <div class Yum
                        className="flex items-center space-x-3">
                          <RadioGroupItem value="Prospect" id="prospect-type" />
                          <Label
                            htmlFor="prospect-type"
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                              formData.client_type === 'Prospect'
                                ? 'bg-purple-50 border-purple-200 text-purple-700'
                                : 'bg-white border-gray-200 text-gray-600'
                            }`}
                          >
                            <Target className="h-4 w-4" />
                            <span className="text-sm font-medium">Prospect</span>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem value="Sale" id="sale-type" />
                          <Label
                            htmlFor="sale-type"
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                              formData.client_type === 'Sale'
                                ? 'bg-green-50 border-green-200 text-green-700'
                                : 'bg-white border-gray-200 text-gray-600'
                            }`}
                          >
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm font-medium">Sale</span>
                          </Label>
                        </div>
                      </RadioGroup>
                    </>
                  ) : (
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-blue-600" />
                        <p className="text-gray-800 font-medium">{getIdentifierValue()}</p>
                      </div>
                    </div>
                  )}
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-blue-600" />
                    <Label htmlFor="marketingTeam" className="text-sm font-medium text-gray-700">
                      Marketing Team {isEditMode && <span className="text-red-500">*</span>}
                    </Label>
                  </div>
                  {isEditMode ? (
                    <Input
                      id="marketingTeam"
                      value={formData.marketingTeam}
                      onChange={handleInputChange}
                      placeholder="Enter marketing team..."
                      className="border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-white"
                    />
                  ) : (
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-blue-600" />
                        <p className="text-gray-800 font-medium">{engagementData?.marketingTeam || 'No team specified'}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-600" />
                  <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                    Description {isEditMode && <span className="text-red-500">*</span>}
                  </Label>
                </div>
                {isEditMode ? (
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Describe the engagement in detail..."
                    rows={4}
                    className="border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-white resize-none"
                  />
                ) : (
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                    <p className="text-gray-800 leading-relaxed">{engagementData?.description || 'No description provided'}</p>
                  </div>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Paperclip className="h-4 w-4 text-blue-600" />
                    <Label htmlFor="attachment" className="text-sm font-medium text-gray-700">
                      Attachment
                    </Label>
                  </div>
                  {isEditMode ? (
                    <Input
                      id="attachment"
                      type="file"
                      onChange={handleFileChange}
                      className="border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-white"
                    />
                  ) : (
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                      <div className="flex items-center gap-2">
                        <Paperclip className="h-4 w-4 text-blue-600" />
                        <p className="text-gray-800 font-medium">
                          {engagementData?.attachment ? engagementData.attachment.name : 'No attachment'}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <Label className="text-sm font-medium text-gray-700">Created Date</Label>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-blue-600" />
                      <p className="text-gray-800 font-medium">
                        {engagementData?.createdDate
                          ? new Date(engagementData.createdDate).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                            })
                          : 'No date specified'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              {engagementData?.participants && engagementData.participants.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <Label className="text-sm font-medium text-gray-700">Participants</Label>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                    <div className="flex flex-wrap gap-2">
                      {engagementData.participants.map((participant) => (
                        <div
                          key={participant.id}
                          className="flex items-center gap-2 bg-white rounded-lg px-3 py-2 border border-blue-200"
                        >
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-medium">
                              {participant.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <span className="text-sm font-medium text-gray-700">{participant.name}</span>
                          <span className="text-xs text-gray-500">({participant.role})</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ),
        },
        {
          title: 'Review & Actions',
          content: (
            <div className="py-6 space-y-6">
              <div className="text-center">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  {isEditMode ? <Save className="h-8 w-8 text-white" /> : <CheckCircle className="h-8 w-8 text-white" />}
                </div>
                <h3 className="text-xl font-semibold text-gray-800">
                  {isEditMode ? 'Save Changes' : 'Review Complete'}
                </h3>
                <p className="text-gray-600 text-sm mt-2">
                  {isEditMode
                    ? 'Please review your changes before saving to the system.'
                    : 'You have successfully reviewed all engagement details.'}
                </p>
              </div>
              {isEditMode && (
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
                  <div className="flex items-center gap-2 mb-4">
                    <Star className="h-5 w-5 text-green-600" />
                    <h4 className="text-lg font-semibold text-gray-800">Changes Summary</h4>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        <span className="text-sm font-semibold">Title:</span> {formData.title || 'Not specified'}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        <span className="font-semibold">Engagement Type:</span> {formData.engagement_type || 'Not specified'}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        <strong>
                          {client_type === 'Customer' ? 'Customer' : client_type === 'Prospect' ? 'Prospect No' : 'Lead No'}:
                        </strong>{' '}
                        {getFormIdentifierValue() || 'Not specified'}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        <span className="font-semibold">Marketing Team:</span> {formData.marketingTeam || 'Not specified'}
                      </span>
                    </div>
                  </div>
                </div>
              )}
              {!isEditMode && (
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200 text-center">
                  <div className="flex items-center justify-center gap-2 text-green-600 mb-2">
                    <Heart className="h-5 w-5" />
                    <span className="font-semibold">Engagement Review Complete</span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    All engagement details have been successfully reviewed. You can now edit the engagement or close this dialog.
                  </p>
                </div>
              )}
              <div className="flex justify-center gap-4">
                {!isEditMode ? (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => setIsEditMode(true)}
                      className="flex items-center gap-2 px-6 py-3 border-2 border-green-200 text-green-700 hover:bg-green-50 rounded-xl font-semibold transition-all duration-200"
                    >
                      <Edit3 className="h-4 w-4" />
                      Edit Engagement
                    </Button>
                    <Button
                      variant="default"
                      onClick={() => onOpenChange(false)}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      <CheckCircle className="h-4 w-4" />
                      Close
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => setIsEditMode(false)}
                      className="flex items-center gap-2 px-6 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 rounded-xl font-semibold transition-all duration-200"
                    >
                      <X className="h-4 w-4" />
                      Cancel
                    </Button>
                    <Button
                      variant="default"
                      onClick={handleSaveChanges}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      <Save className="h-4 w-4" />
                      Save Changes
                    </Button>
                  </>
                )}
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 text-gray-500 text-xs">
                  <Star className="h-3 w-3" />
                  <span>Engagement management made simple</span>
                  <Star className="h-3 w-3" />
                </div>
              </div>
            </div>
          ),
        },
      ]}
    />
  );
}