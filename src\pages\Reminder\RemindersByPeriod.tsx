import { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import {
  Calendar,
  Clock,
  AlertTriangle,
  AlertCircle,
  Circle,
  CheckCircle2,
  CalendarDays,
  Filter,
} from "lucide-react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  useGetRemindersByPeriodQuery,
  useGetOverdueRemindersQuery,
  useGetUpcomingRemindersQuery,
} from "@/redux/slices/reminderApiSlice";
import { useToast } from "@/hooks/use-toast";

export default function RemindersByPeriod() {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [viewMode, setViewMode] = useState<"period" | "overdue" | "upcoming">("period");
  const { toast } = useToast();

  // API hooks
  const {
    data: periodRemindersData,
    error: periodError,
    isLoading: periodLoading,
    refetch: refetchPeriod,
  } = useGetRemindersByPeriodQuery(
    { start_date: startDate, end_date: endDate },
    { skip: !startDate || !endDate }
  );

  const {
    data: overdueRemindersData,
    error: overdueError,
    isLoading: overdueLoading,
    refetch: refetchOverdue,
  } = useGetOverdueRemindersQuery({});

  const {
    data: upcomingRemindersData,
    error: upcomingError,
    isLoading: upcomingLoading,
    refetch: refetchUpcoming,
  } = useGetUpcomingRemindersQuery({});

  // Helper functions
  const getPriorityIcon = (priority?: string) => {
    switch (priority?.toLowerCase()) {
      case "critical":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "high":
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case "normal":
        return <Circle className="h-4 w-4 text-blue-500" />;
      case "low":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default:
        return <Circle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "active":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "snoozed":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getCurrentData = () => {
    switch (viewMode) {
      case "overdue":
        return overdueRemindersData;
      case "upcoming":
        return upcomingRemindersData;
      default:
        return periodRemindersData;
    }
  };

  const getCurrentLoading = () => {
    switch (viewMode) {
      case "overdue":
        return overdueLoading;
      case "upcoming":
        return upcomingLoading;
      default:
        return periodLoading;
    }
  };

  const getCurrentError = () => {
    switch (viewMode) {
      case "overdue":
        return overdueError;
      case "upcoming":
        return upcomingError;
      default:
        return periodError;
    }
  };

  const getCurrentRefetch = () => {
    switch (viewMode) {
      case "overdue":
        return refetchOverdue;
      case "upcoming":
        return refetchUpcoming;
      default:
        return refetchPeriod;
    }
  };

  const currentData = getCurrentData();
  const currentLoading = getCurrentLoading();
  const currentError = getCurrentError();
  const currentRefetch = getCurrentRefetch();
  const reminders = currentData?.results || currentData?.data || [];

  // Combine reminder_date and reminder_time into a Date object
  const getReminderDateTime = (reminder: any) => {
    return new Date(`${reminder.reminder_date}T${reminder.reminder_time}`);
  };

  // Determine if a reminder is overdue
  const isReminderOverdue = (reminder: any) => {
    if (viewMode === "overdue") return true; // Overdue view already filters overdue reminders
    const reminderDateTime = getReminderDateTime(reminder);
    return reminderDateTime < new Date() && reminder.status?.toLowerCase() !== "completed";
  };

  const handleSetThisWeek = () => {
    const today = new Date();
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));

    setStartDate(startOfWeek.toISOString().split("T")[0]);
    setEndDate(endOfWeek.toISOString().split("T")[0]);
  };

  const handleSetThisMonth = () => {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    setStartDate(startOfMonth.toISOString().split("T")[0]);
    setEndDate(endOfMonth.toISOString().split("T")[0]);
  };

  return (
    <Screen>
      {/* Header */}
      <header className="flex-none px-6 py-4 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CalendarDays className="h-6 w-6 text-green-700" />
            <div>
              <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {viewMode === "overdue" && "⚠️ Overdue Reminders"}
                {viewMode === "upcoming" && "🔜 Upcoming Reminders"}
                {viewMode === "period" && "📅 Reminders by Period"}
              </h1>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {reminders.length} reminder{reminders.length !== 1 ? "s" : ""} found
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <PrimaryButton
              onClick={() => setViewMode("period")}
              variant={viewMode === "period" ? "default" : "outline"}
              className={
                viewMode === "period"
                  ? "bg-green-600 hover:bg-green-700"
                  : "border-green-200 text-green-700 hover:bg-green-50"
              }
            >
              📅 Period View
            </PrimaryButton>
            <PrimaryButton
              onClick={() => setViewMode("upcoming")}
              variant={viewMode === "upcoming" ? "default" : "outline"}
              className={
                viewMode === "upcoming"
                  ? "bg-blue-600 hover:bg-blue-700"
                  : "border-blue-200 text-blue-700 hover:bg-blue-50"
              }
            >
              🔜 Upcoming
            </PrimaryButton>
            <PrimaryButton
              onClick={() => setViewMode("overdue")}
              variant={viewMode === "overdue" ? "default" : "outline"}
              className={
                viewMode === "overdue"
                  ? "bg-red-600 hover:bg-red-700"
                  : "border-red-200 text-red-700 hover:bg-red-50"
              }
            >
              ⚠️ Overdue
            </PrimaryButton>
          </div>
        </div>
      </header>

      {/* Filters */}
      {viewMode === "period" && (
        <div className="flex-none p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200">
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Filter className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Date Range Filter:</span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
              <div className="space-y-2">
                <Label htmlFor="start-date" className="text-sm font-medium text-gray-700">
                  Start Date
                </Label>
                <Input
                  id="start-date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="border-green-200 focus:border-green-400 focus:ring-green-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="end-date" className="text-sm font-medium text-gray-700">
                  End Date
                </Label>
                <Input
                  id="end-date"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="border-green-200 focus:border-green-400 focus:ring-green-200"
                />
              </div>

              <Button
                onClick={handleSetThisWeek}
                variant="outline"
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                This Week
              </Button>

              <Button
                onClick={handleSetThisMonth}
                variant="outline"
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                This Month
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 p-6 overflow-auto">
        {currentLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700 mx-auto mb-4"></div>
              <p className="text-slate-600 dark:text-slate-400">
                Loading {viewMode} reminders...
              </p>
            </div>
          </div>
        ) : currentError ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400 mb-4">
                Error loading reminders
              </p>
              <PrimaryButton onClick={() => currentRefetch()}>Try Again</PrimaryButton>
            </div>
          </div>
        ) : (
          <ScrollArea className="h-full">
            {reminders.length > 0 ? (
              <div className="grid gap-4">
                {reminders.map((reminder: any) => (
                  <Card
                    key={reminder.reminder_id}
                    className="border-2 hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-gray-50"
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div>
                            <h3 className="font-semibold text-gray-800 text-lg">{reminder.title}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge className={`text-xs ${getStatusColor(reminder.status)}`}>
                                {reminder.status}
                              </Badge>
                              {reminder.priority && (
                                <Badge variant="outline" className="text-xs">
                                  <div className="flex items-center gap-1">
                                    {getPriorityIcon(reminder.priority)}
                                    {reminder.priority}
                                  </div>
                                </Badge>
                              )}
                              {isReminderOverdue(reminder) && (
                                <Badge className="bg-red-100 text-red-800 border-red-200">
                                  ⚠️ Overdue
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0 space-y-3">
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(reminder.reminder_date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>
                            {new Date(
                              `${reminder.reminder_date}T${reminder.reminder_time}`
                            ).toLocaleTimeString("en-US", {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </span>
                        </div>
                      </div>

                      {reminder.prospect && reminder.client_type === "Prospect" && (
                        <p className="text-sm text-gray-600">
                          <strong>Prospect:</strong> {reminder.prospect}
                        </p>
                      )}
                      {reminder.description && (
                        <p className="text-sm text-gray-600">
                          <strong>Description:</strong> {reminder.description}
                        </p>
                      )}
                      {reminder.reminder_notes && (
                        <p className="text-sm text-gray-600">
                          <strong>Notes:</strong> {reminder.reminder_notes}
                        </p>
                      )}
                      {reminder.reminder_type && (
                        <p className="text-sm text-gray-600">
                          <strong>Type:</strong> {reminder.reminder_type}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="border-2 border-dashed border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
                <CardContent className="flex flex-col items-center justify-center h-64 text-center p-6">
                  <div className="mb-4 p-4 rounded-full bg-green-100">
                    <CalendarDays className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-green-800 mb-2">
                    No {viewMode} reminders found
                  </h3>
                  <p className="text-green-600 mb-4 text-sm">
                    {viewMode === "overdue" && "Great! You have no overdue reminders."}
                    {viewMode === "upcoming" && "You don't have any upcoming reminders scheduled."}
                    {viewMode === "period" && "Try selecting a different date range to see reminders."}
                  </p>
                </CardContent>
              </Card>
            )}
          </ScrollArea>
        )}
      </div>
    </Screen>
  );
}
