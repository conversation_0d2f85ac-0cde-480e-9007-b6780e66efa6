import { contentHeader, noAuthHeader } from "../../utils/header";
import { apiSlice } from "../apiSlice";

export const feedbackApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getFeedback: builder.query({
      query: (params) => ({
        url: "/services/feedback/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        console.log("Feedback API Response:", response);

        // Transform the response to match expected structure
        return {
          results: response.data.results || [],
          count: response.data.count || 0,
          current_page: response.data.current_page || 1,
          num_pages: response.data.num_pages || 1,
          total_data: response.data.count || 0,
          per_page: Math.ceil(
            (response.data.count || 0) / (response.data.num_pages || 1)
          ),
        };
      },
      transformErrorResponse: (error) => {
        console.error("Feedback API Error:", error);
        return error;
      },
      providesTags: ["Feedback"],
    }),

    getFeedbackDetails: builder.query({
      query: (feedbackId) => ({
        url: `/services/feedback/${feedbackId}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Feedback"],
    }),

    createFeedback: builder.mutation({
      query: (data) => ({
        url: "/services/feedback/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Feedback"],
    }),

    updateFeedback: builder.mutation({
      query: ({ feedbackId, data }) => ({
        url: `/services/feedback/${feedbackId}/`,
        method: "PUT",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Feedback"],
    }),

    partialUpdateFeedback: builder.mutation({
      query: (data) => ({
        url: `/services/feedback/${data?.feedback_id}`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Feedback"],
    }),

    deleteFeedback: builder.mutation({
      query: (feedbackId) => ({
        url: `/services/feedback/${feedbackId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Feedback"],
    }),

    respondToFeedback: builder.mutation({
      query: ({ feedbackId, data }) => ({
        url: `/services/feedback/${feedbackId}/respond/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Feedback"],
    }),
  }),
});

export const {
  useGetFeedbackQuery,
  useGetFeedbackDetailsQuery,
  useCreateFeedbackMutation,
  useUpdateFeedbackMutation,
  usePartialUpdateFeedbackMutation,
  useDeleteFeedbackMutation,
  useRespondToFeedbackMutation,

  useLazyGetFeedbackQuery,
} = feedbackApiSlice;
