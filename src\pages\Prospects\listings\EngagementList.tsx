import BaseModal from "@/components/custom/modals/BaseModal";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate, formatDateTime } from "@/utils/formatDate";
import { Edit } from "lucide-react";
import { useState } from "react";
import EngagementForm from "../forms/EngagementForm";
import { Link } from "react-router-dom";

type Props = {
  data: any;
};

const EngagementList = ({ data }: Props) => {
  const [EngagementModalOpen, setEngagementModalOpen] = useState<any | null>(
    null
  );
  return (
    <div className="border  rounded">
      <Table>
        <TableHeader className="bg-accent">
          <TableRow className="!font-bold">
            <TableHead className="!font-bold">Type</TableHead>
            <TableHead className="!font-bold">Subject</TableHead>
            <TableHead className="!font-bold">Description</TableHead>
            <TableHead className="!font-bold">Follow Up</TableHead>
            <TableHead className="!font-bold">Reminder</TableHead>
            <TableHead className="!font-bold">Created on</TableHead>
            <TableHead className="!font-bold">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.map((rowData: any) => (
            <TableRow
              key={rowData?.engagement_id}
              className="hover:!bg-transparent bg-white dark:bg-transparent dark:hover:!bg-gray-300/10"
            >
              <TableCell className="!py-2.5 text-xs">
                {rowData?.engagement_type}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.subject}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.description}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.follow_up_date
                  ? formatDate(rowData?.follow_up_date)
                  : "N/A"}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.reminder_time
                  ? formatDateTime(rowData?.reminder_time)
                  : "N/A"}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {formatDateTime(rowData?.created_at)}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Edit
                  className="cursor-pointer text-blue-500 hover:text-blue-700"
                  onClick={() => setEngagementModalOpen(rowData)}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <BaseModal
        isOpen={!!EngagementModalOpen}
        onOpenChange={() => setEngagementModalOpen(null)}
        title="Engagement"
        description="Manage your Engagement here"
      >
        <EngagementForm updateData={EngagementModalOpen} />
      </BaseModal>
    </div>
  );
};

export default EngagementList;
