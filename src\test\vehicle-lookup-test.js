// Test file to debug vehicle lookup issue for special assignments

// Sample vehicles data (similar to what comes from API)
const sampleVehiclesData = {
  data: {
    results: [
      {
        id: 1,
        make: "Toyota",
        model: "Hiace",
        vehicle_registration: "KCA 123A",
        status: "Available"
      },
      {
        id: 2,
        make: "Nissan",
        model: "Urvan",
        vehicle_registration: "KCB 456B",
        status: "Available"
      },
      {
        id: 3,
        make: "Isuzu",
        model: "D-Max",
        vehicle_registration: "KCC 789C",
        status: "In Use"
      }
    ]
  }
};

// Sample special assignment data (similar to what comes from API)
const sampleSpecialAssignment = {
  id: 100,
  reservation_date: "2025-01-15",
  reservation_time: "09:00:00",
  pickup_location: "Nairobi CBD",
  destination: "ICT Department",
  vehicle: 2, // Should match vehicle ID 2 (Nissan Urvan)
  driver: "<PERSON>",
  assigned_to: "ICT SYSTEMS & CyberSecurity",
  status: "Approved",
  is_special_assignment: true
};

// Test the vehicle lookup logic
function testVehicleLookup(visit, vehiclesData) {
  const vehicles = vehiclesData?.data?.results || [];
  
  console.log('🧪 TESTING VEHICLE LOOKUP');
  console.log('Visit:', visit);
  console.log('Available vehicles:', vehicles.length);
  
  // For special assignments, look up vehicle by ID
  if (visit.is_special_assignment) {
    // Try multiple possible vehicle field names
    const possibleVehicleFields = ['vehicle', 'vehicle_id', 'assigned_vehicle'];
    let vehicleId = null;
    let vehicleFieldUsed = null;
    
    for (const field of possibleVehicleFields) {
      if (visit[field] !== null && visit[field] !== undefined) {
        vehicleId = typeof visit[field] === 'string' ? parseInt(visit[field], 10) : visit[field];
        vehicleFieldUsed = field;
        break;
      }
    }
    
    console.log('🔍 Vehicle field analysis:', {
      vehicleFieldUsed,
      originalValue: vehicleFieldUsed ? visit[vehicleFieldUsed] : null,
      parsedVehicleId: vehicleId,
      isValidNumber: vehicleId !== null && !isNaN(vehicleId) && vehicleId > 0
    });
    
    if (vehicleId !== null && !isNaN(vehicleId) && vehicleId > 0) {
      const vehicleData = vehicles.find((v) => v.id === vehicleId);
      console.log('🔍 Vehicle lookup result:', vehicleData);
      
      if (vehicleData) {
        const result = {
          make: vehicleData.make,
          model: vehicleData.model,
          registration: vehicleData.vehicle_registration || vehicleData.registration,
          id: vehicleData.id
        };
        console.log('✅ Vehicle info result:', result);
        return result;
      } else {
        console.log('❌ No vehicle found with ID:', vehicleId);
        console.log('❌ Available vehicle IDs:', vehicles.map(v => v.id));
        return {
          make: `Vehicle ID: ${vehicleId}`,
          model: '',
          registration: '',
          id: vehicleId
        };
      }
    } else {
      console.log('❌ No valid vehicle ID found');
      return {
        make: null,
        model: null,
        registration: null
      };
    }
  }
  
  return {
    make: null,
    model: null,
    registration: null
  };
}

// Run the test
console.log('='.repeat(50));
console.log('VEHICLE LOOKUP TEST');
console.log('='.repeat(50));

const result = testVehicleLookup(sampleSpecialAssignment, sampleVehiclesData);
console.log('Final result:', result);

// Expected result should be:
// {
//   make: "Nissan",
//   model: "Urvan", 
//   registration: "KCB 456B",
//   id: 2
// }

console.log('='.repeat(50));
console.log('TEST COMPLETE');
console.log('='.repeat(50));
