import { contentHeader, noAuthHeader } from "../../utils/header";
import { apiSlice } from "../apiSlice";

export const reminderApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getReminders: builder.query({
      query: (params) => ({
        url: "/reminders/all-reminders",
        method: "GET",
        headers: noAuthHeader(),
        params,
      }),
      transformResponse: (response: any) => {
        console.log("Reminders API Response:", response);

        // Handle the actual API response structure
        const results = response?.data?.results || [];
        console.log("Extracted results:", results);

        return {
          ...response.data,
          data: results, // Map results to data for backward compatibility
          results: results,
        };
      },
      transformErrorResponse: (error) => {
        console.error("Reminders API Error:", error);
        return error;
      },
      providesTags: (result) => {
        const reminders = Array.isArray(result?.results)
          ? result.results
          : Array.isArray(result?.data)
          ? result.data
          : [];
        console.log("Reminders for tags:", reminders);
        return reminders.length > 0
          ? reminders.map((reminder: { reminder_id: any }) => ({
              type: "Reminders",
              id: reminder.reminder_id,
            }))
          : [{ type: "Reminders" }];
      },
    }),

    getReminderById: builder.query({
      query: (id) => ({
        url: `/reminders/all-reminders/${id}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: (result, error, id) => [{ type: "Reminders", id }],
    }),

    // Create a new reminder
    addReminder: builder.mutation({
      query: (newReminder) => ({
        url: "/reminders/all-reminders",
        method: "POST",
        headers: contentHeader(),
        body: newReminder,
      }),
      invalidatesTags: ["Reminders"],
    }),

    // Update an existing reminder by its ID
    updateReminder: builder.mutation({
      query: ({ id, ...updatedReminder }) => ({
        url: `/reminders/all-reminders/${id}`,
        method: "PUT",
        headers: contentHeader(),
        body: updatedReminder,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Reminders", id }],
    }),

    deleteReminder: builder.mutation({
      query: (id) => ({
        url: `/reminders/all-reminders/${id}`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: (result, error, id) => [{ type: "Reminders", id }],
    }),

    snoozeReminder: builder.mutation({
      query: ({ id, remind_at }) => ({
        url: `/reminders/all-reminders/${id}/snooze`,
        method: "PATCH",
        headers: contentHeader(),
        body: { remind_at },
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Reminders", id }],
    }),

    completeReminder: builder.mutation({
      query: ({ id, remind_at }) => ({
        url: `/reminders/all-reminders/${id}/complete/`,
        method: "PATCH",
        headers: contentHeader(),
        body: { remind_at },
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Reminders", id }],
    }),

    // Get reminders by period
    getRemindersByPeriod: builder.query({
      query: (params) => ({
        url: "/reminders/all-reminders/by_period/",
        method: "GET",
        headers: noAuthHeader(),
        params,
      }),
      transformResponse: (response: any) => {
        console.log("Reminders by Period API Response:", response);

        // Handle the API response structure
        const results = response?.data?.results || [];
        console.log("Extracted period results:", results);

        return {
          ...response.data,
          data: results, // Map results to data for backward compatibility
          results: results,
        };
      },
      transformErrorResponse: (error) => {
        console.error("Reminders by Period API Error:", error);
        return error;
      },
      providesTags: ["Reminders"],
    }),

    // Get overdue reminders
    getOverdueReminders: builder.query({
      query: (params) => ({
        url: "/reminders/all-reminders/overdue/",
        method: "GET",
        headers: noAuthHeader(),
        params,
      }),
      transformResponse: (response: any) => {
        console.log("Overdue Reminders API Response:", response);

        // Handle the API response structure
        const results = response?.data?.results || [];
        console.log("Extracted overdue results:", results);

        return {
          ...response.data,
          data: results, // Map results to data for backward compatibility
          results: results,
        };
      },
      transformErrorResponse: (error) => {
        console.error("Overdue Reminders API Error:", error);
        return error;
      },
      providesTags: ["Reminders"],
    }),

    // Get upcoming reminders
    getUpcomingReminders: builder.query({
      query: (params) => ({
        url: "/reminders/all-reminders/upcoming/",
        method: "GET",
        headers: noAuthHeader(),
        params,
      }),
      transformResponse: (response: any) => {
        console.log("Upcoming Reminders API Response:", response);

        // Handle the API response structure
        const results = response?.data?.results || [];
        console.log("Extracted upcoming results:", results);

        return {
          ...response.data,
          data: results, // Map results to data for backward compatibility
          results: results,
        };
      },
      transformErrorResponse: (error) => {
        console.error("Upcoming Reminders API Error:", error);
        return error;
      },
      providesTags: ["Reminders"],
    }),
  }),
});

export const {
  useGetRemindersQuery,
  useGetReminderByIdQuery,
  useAddReminderMutation,
  useUpdateReminderMutation,
  useDeleteReminderMutation,
  useSnoozeReminderMutation,
  useGetRemindersByPeriodQuery,
  useGetOverdueRemindersQuery,
  useGetUpcomingRemindersQuery,
  useCompleteReminderMutation,

  useLazyGetRemindersQuery,
} = reminderApiSlice;
