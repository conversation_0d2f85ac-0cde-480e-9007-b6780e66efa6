import BaseModal from "@/components/custom/modals/BaseModal";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Edit } from "lucide-react";
import FlagsForm from "../forms/FlagsForm";
import { useState } from "react";
import { formatDate, formatDateTime } from "@/utils/formatDate";

type Props = {
  data: any;
};

const FlagsList = ({ data }: Props) => {
  const [FlagsModalOpen, setFlagsModalOpen] = useState<any | null>(null);
  return (
    <div className="border  rounded">
      <Table>
        <TableHeader className="bg-accent">
          <TableRow className="!font-bold">
            <TableHead className="!font-bold">Reason</TableHead>
            <TableHead className="!font-bold">Description</TableHead>
            <TableHead className="!font-bold">Follow Up</TableHead>
            <TableHead className="!font-bold">Reminder</TableHead>
            <TableHead className="!font-bold">Status</TableHead>
            <TableHead className="!font-bold">Resolution Note</TableHead>
            <TableHead className="!font-bold">Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.map((rowData: any) => (
            <TableRow
              key={rowData?.flag_id}
              className="hover:!bg-transparent bg-white dark:bg-transparent dark:hover:!bg-gray-300/10"
            >
              <TableCell className="!py-2.5 text-xs">
                {rowData?.flag_reason}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.description}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.follow_up_date
                  ? formatDate(rowData?.follow_up_date)
                  : "N/A"}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.reminder_time
                  ? formatDateTime(rowData?.reminder_time)
                  : "N/A"}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Badge variant="default" className="px-4">
                  {rowData?.is_resolved ? "Resolved" : "Active"}
                </Badge>
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.resolution_date
                  ? formatDateTime(rowData?.resolution_date)
                  : "N/A"}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Edit
                  className="cursor-pointer text-blue-500 hover:text-blue-700"
                  onClick={() => setFlagsModalOpen(rowData)}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <BaseModal
        isOpen={!!FlagsModalOpen}
        onOpenChange={() => setFlagsModalOpen(null)}
        title="Flags"
        description="Manage your Flags here"
      >
        <FlagsForm updateData={FlagsModalOpen} />
      </BaseModal>
    </div>
  );
};

export default FlagsList;
