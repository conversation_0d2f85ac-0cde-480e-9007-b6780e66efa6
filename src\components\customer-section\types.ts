// types.ts
export interface CustomerSidebarProps {
  className?: string;
  customerNo?: string;
  leadfileNo?: string;
  entityType?: "customer" | "prospect" | "leadfile";
  salesCardCustomerId?: string;
  entityId?: string;
  currentUser?: {
    id: string;
    name: string;
  };
  initialExpandedSection?: keyof ExpandedSections;
}

export interface ComplaintFormData {
  title: string;
  description: string;
  category: string;
  priority: string;
  assigned_to?: string;
  due_date?: string;
  entity_type: string;
  entity_id: string;
  customer_no?: string;
  prospect_id?: string;
  lead_file_no?: string;
}

export interface EngagementFormData {
  client_type: string; // "Prospect" | "Customer" | "Sale"
  engagement_type: string; // "Call" | "Email" | "Meeting" | "SMS" | "Chat" | "Visit" | "Event" | "Follow-up" | "Contact-add"
  subject: string;
  description: string;
  follow_up_required: boolean;
  follow_up_date?: string;
  set_reminder: boolean;
  reminder_time?: string;
  created_by?: string;
  customer?: string; // customer_no for Customer client_type
  prospect?: number; // prospect_id for Prospect client_type
  sale?: string; // lead_file_no for Sale client_type

  // Legacy fields for backward compatibility - will be removed from form data before sending
  entity_type?: string;
  entity_id?: string;
  customer_no?: string;
  prospect_id?: string;
  lead_file_no?: string;
  client_status?: string;
  status?: string;
  outcome?: string;
  scheduled_at?: string;
  started_at?: string;
  completed_at?: string;
  duration_minutes?: number;
  assigned_to?: string;
  notes?: string;
}

export interface ExpandedSections {
  reminders: boolean;
  tickets: boolean;
  notes: boolean;
  flags: boolean;
  engagements: boolean;
  notifications: boolean;
}

export interface FeedbackFormData {
  entity_type: string;
  entity_id: string;
  customer_no?: string;
  prospect_id?: string;
  lead_file_no?: string;
  client_status: string;
  feedback_type: string;
  subject: string;
  message: string;
  rating?: number;
  is_anonymous: boolean;
  is_public: boolean;
  response?: string;
}

export interface FeedbackItem {
  feedback_id: string;
  subject: string;
  message: string;
  feedback_type: string;
  rating?: number;
  created_at: string;
  entity_type: string;
  entity_name: string;
  is_anonymous: boolean;
  is_public: boolean;
  response?: string;
  responded_at?: string;
  responded_by_name?: string;
  customer_info?: string;
  prospect_info?: string;
  lead_file_info?: string;
}
// Tickets-related interfaces
export interface TicketFormData {
  title: string;
  description: string;
  customer: string;
  user?: string; // Added user field for assignment
  category?: number;
  prospect?: string;
  sales?: string; // Added sales field for sales cards
  priority: "low" | "medium" | "high" | "critical";
  status: "open" | "in_progress" | "resolved" | "closed" | "escalated";
  source?: number;
  actions: number[]; // Required by API
  attachments: number[]; // Required by API
  messages: number[]; // Required by API
}

export interface TicketItem {
  id: number;
  ticket_id: string;
  title: string;
  description: string;
  customer: string;
  user?: string; // Made optional to match API response
  category?: number; // Made optional to match API response
  prospect?: string;
  sales?: string; // Added sales field for sales cards
  priority: "low" | "medium" | "high" | "critical";
  status: "open" | "in_progress" | "resolved" | "closed" | "escalated";
  created_at: string;
  updated_at: string;
  attachments: number[];
  messages: number[];
  actions: number[];
  source?: number;
  source_name?: string;
  category_name?: string;
  customer_name?: string;
  user_name?: string;
  sales_number?: string; // Added for sales information
  prospect_name?: string; // Added for prospect information
}

export interface TicketMessage {
  id: number;
  ticket: number;
  sender: string;
  message: string;
  created_at: string;
  ticket_id?: string;
  sender_name?: string;
}

export interface TicketAttachment {
  id: number;
  ticket: number;
  uploaded_by: number;
  file: string;
  uploaded_at: string;
}

export interface TicketActionLog {
  id: number;
  ticket: number;
  action: "created" | "assigned" | "updated" | "escalated" | "resolved" | "reopened" | "closed" | "commented";
  performed_by?: number;
  comment?: string;
  timestamp: string;
}

export interface TicketCategory {
  id: number;
  name: string;
  description?: string;
}

export interface TicketSource {
  id: number;
  name: string;
  description?: string;
}

export interface TicketMessageFormData {
  ticket: number;
  sender: string;
  message: string;
}

export interface TicketAttachmentFormData {
  ticket: number;
  uploaded_by: number;
  file: File;
}

export interface TicketCategoryFormData {
  name: string;
  description?: string;
}

export interface TicketSourceFormData {
  name: string;
  description?: string;
}

export interface TicketActionLogFormData {
  ticket: number;
  action: "created" | "assigned" | "updated" | "escalated" | "resolved" | "reopened" | "closed" | "commented";
  performed_by?: number;
  comment?: string;
}

export interface ReminderFormData {
  // New API fields
  client_type?: string;
  reminder_type: string;
  reminder_date?: string;
  reminder_time?: string;
  priority: string;
  reminder_notes?: string;
  action_url?: string;
  title: string;
  description?: string;
  status: string;
  created_by?: string;
  customer?: string;
  prospect?: number;
  sale?: string;

  // Legacy fields for backward compatibility
  entity_type: "customer" | "prospect" | "leadfile";
  entity_id: string;
  customer_no: string;
  prospect_id: string;
  lead_file_no: string;
  client_status: string;
  remind_at: string;
  advance_notice_minutes: number;
  repeat_pattern: string;
  tags: string;
  is_active: boolean;
}

export type ReminderType = "Follow-up Call" | "Payment Reminder" | "Document Collection" | "Site Visit" | "Meeting" | "Email" | "SMS" | "General";
export type ReminderPriority = "Low" | "Normal" | "High" | "Urgent";
export type ReminderStatus = "Active" | "Completed" | "Cancelled" | "Snoozed";
export type RepeatPattern = "None" | "Daily" | "Weekly" | "Monthly" | "Yearly";
export type TimePeriod = "today" | "tomorrow" | "this_week" | "this_month";

export type BadgeRenderer = (value: string) => JSX.Element;
export type DateFormatter = (dateString: string) => string;