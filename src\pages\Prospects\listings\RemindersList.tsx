import { formatDate, formatDateTime } from "@/utils/formatDate";
import { useState } from "react";

type Props = {
  data: any;
};

const RemindersList = ({ data }: Props) => {
  const [RemindersModalOpen, setRemindersModalOpen] = useState<any | null>(
    null
  );

  return (
    <div className="grid md:grid-cols-4 sm:grid-cols-2 gap-4 border-t pt-4  ">
      {data?.map((rowData: any) => (
        <div
          key={rowData?.reminder_id}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden border border-gray-200 dark:border-gray-700"
        >
          <div className="flex flex-col h-full">
            <div className="bg-gradient-to-r from-yellow-400 to-orange-400 p-2">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-white px-2 py-1 rounded-full bg-white/20">
                  {rowData?.reminder_type}
                </span>
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    rowData?.priority === "high"
                      ? "bg-red-500"
                      : rowData?.priority === "medium"
                      ? "bg-yellow-500"
                      : "bg-green-500"
                  } text-white`}
                >
                  {rowData?.priority}
                </span>
              </div>
            </div>
            <div className="p-3 flex-1 space-y-2">
              <h3 className="text-sm font-medium capitalize text-gray-900 dark:text-gray-100 line-clamp-1">
                {rowData?.title ? rowData?.title?.toLowerCase() : "Title"}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2">
                {rowData?.description}
              </p>
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center gap-2">
                  <span>
                    Due: {formatDate(rowData?.reminder_date)}{" "}
                    {rowData?.reminder_time}
                  </span>
                </div>
                <span>{rowData?.entity_name}</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-blue-500">
                  {formatDateTime(rowData?.created_at)}
                </span>
                <span
                  className={`${
                    rowData?.status === "completed"
                      ? "text-green-500"
                      : rowData?.status === "pending"
                      ? "text-yellow-500"
                      : "text-red-500"
                  }`}
                >
                  {rowData?.status}
                </span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default RemindersList;
