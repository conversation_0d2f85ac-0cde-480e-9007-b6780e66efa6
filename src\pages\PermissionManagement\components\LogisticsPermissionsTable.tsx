import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface LogisticsPermission {
  id: number;
  name: string;
  code: string;
  description: string;
}

const logisticsPermissions: LogisticsPermission[] = [
  {
    id: 201,
    name: "BOOK_VISIT",
    code: "201",
    description: "Allows user to book a site visit or appointment"
  },
  {
    id: 202,
    name: "COMPLETE_TRIPS",
    code: "202",
    description: "Allows user to mark trips as completed"
  },
  {
    id: 203,
    name: "CREATE_VEHICLE_REQUEST",
    code: "203",
    description: "Allows user to request a vehicle"
  },
  {
    id: 204,
    name: "CREATE_SPECIAL_ASSIGNMENT",
    code: "204",
    description: "Allows user to assign or create special assignments"
  },
  {
    id: 205,
    name: "ACCESS_LOGISTICS_DASHBOARD",
    code: "205",
    description: "Allows user to view the logistics dashboard"
  },
  {
    id: 206,
    name: "ACCESS_LOGISTICS_STATISTICS",
    code: "206",
    description: "Allows user to view logistics-related statistics"
  },
  {
    id: 207,
    name: "ACCESS_CLIENTS",
    code: "207",
    description: "Allows user to view clients within logistics module"
  },
  {
    id: 208,
    name: "ACCESS_DRIVERS",
    code: "208",
    description: "Allows user to view and manage drivers"
  },
  {
    id: 209,
    name: "ACCESS_VEHICLES",
    code: "209",
    description: "Allows user to view and manage vehicles"
  },
  {
    id: 210,
    name: "ACCESS_LOGISTICS_REPORTS",
    code: "210",
    description: "Allows user to view and generate logistics reports"
  },
  {
    id: 211,
    name: "ACCESS_SITEVISIT_REPORTS",
    code: "211",
    description: "Allows user to view and generate site visit reports"
  }
];

const LogisticsPermissionsTable: React.FC = () => {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Logistics Permissions Reference</h2>
      <p className="text-sm text-muted-foreground">
        These permissions control access to various logistics features and functionalities within the system.
      </p>
      
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Permission Name</TableHead>
            <TableHead>Code</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Category</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {logisticsPermissions.map((permission) => (
            <TableRow key={permission.id}>
              <TableCell className="font-medium">{permission.name}</TableCell>
              <TableCell>
                <Badge variant="secondary">{permission.code}</Badge>
              </TableCell>
              <TableCell>{permission.description}</TableCell>
              <TableCell>
                {permission.id >= 201 && permission.id <= 204 && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">Actions</Badge>
                )}
                {permission.id >= 205 && permission.id <= 206 && (
                  <Badge variant="outline" className="bg-green-100 text-green-800">Dashboard</Badge>
                )}
                {permission.id >= 207 && permission.id <= 209 && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Data Access</Badge>
                )}
                {permission.id >= 210 && permission.id <= 211 && (
                  <Badge variant="outline" className="bg-orange-100 text-orange-800">Reports</Badge>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      <div className="mt-6 p-4 bg-muted/50 rounded-lg">
        <h3 className="font-medium mb-2">Permission Categories:</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="bg-blue-100 text-blue-800">Actions</Badge>
            <span>User actions and operations</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="bg-green-100 text-green-800">Dashboard</Badge>
            <span>Dashboard and statistics access</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="bg-purple-100 text-purple-800">Data Access</Badge>
            <span>Access to core data entities</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="bg-orange-100 text-orange-800">Reports</Badge>
            <span>Report generation and viewing</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogisticsPermissionsTable;
