import React, { useState, useMemo, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Calendar, Plus, CheckSquare, Clock, Search, Flag } from "lucide-react";
import { useCreateTodoMutation } from "@/redux/slices/todoApiSlice";
import { useGetUsersQuery } from "@/redux/slices/user";
import { useAuthHook } from "@/utils/useAuthHook";

// Custom hook for debounced search
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debouncedValue;
};

// Priority options with colors and icons
const PRIORITY_OPTIONS = [
  { value: "LOW", label: "Low", color: "from-green-500 to-emerald-500", icon: CheckSquare },
  { value: "MEDIUM", label: "Medium", color: "from-yellow-500 to-orange-500", icon: Clock },
  { value: "HIGH", label: "High", color: "from-orange-500 to-red-500", icon: Flag },
  { value: "URGENT", label: "Urgent", color: "from-red-500 to-pink-500", icon: Flag },
];

// Valid status enums as confirmed
const VALID_STATUSES = ["PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED"];

// Client type options
const CLIENT_TYPE_OPTIONS = [
  { value: "none", label: "None (Standalone)" },
  { value: "Customer", label: "Customer" },
  { value: "Prospect", label: "Prospect" },
  { value: "Sale", label: "Sale" },
];

// Map for converting column IDs to valid statuses
const columnToStatusMap: { [key: string]: string } = {
  // Primary mappings
  todo: "PENDING",
  "in-progress": "IN_PROGRESS",
  done: "COMPLETED",
  cancelled: "CANCELLED",
  
  // Alternative formats
  pending: "PENDING",
  in_progress: "IN_PROGRESS",
  completed: "COMPLETED",
  
  // Additional common variations
  "to-do": "PENDING",
  doing: "IN_PROGRESS",
  finished: "COMPLETED",
  complete: "COMPLETED",
  canceled: "CANCELLED", // Alternative spelling
};

// Component props interface
interface AddCardModalProps {
  isOpen: boolean;
  onClose: () => void;
  columnId: string;
  columnTitle: string;
  onAddCard: (card: any) => void;
}

// Initial form data structure
const initialFormData = {
  title: "",
  description: "",
  priority: "LOW",
  dueDate: "",
  dueTime: "",
  status: "",
  assignees: [] as string[],
  client_type: "none",
  customer: "",
  prospect: 0,
  sale: "",
  set_reminder: false,
  reminder_time: "",
};

export const AddCardModal: React.FC<AddCardModalProps> = ({
  isOpen,
  onClose,
  columnId,
  columnTitle,
  onAddCard,
}) => {
  const { user_details } = useAuthHook();

  // Determine valid status from columnId
  const validStatus = columnId
    ? columnToStatusMap[columnId.toLowerCase()] || 
      (VALID_STATUSES.includes(columnId.toUpperCase()) ? columnId.toUpperCase() : "PENDING")
    : "PENDING";

  // Debug logging
  useEffect(() => {
    if (isOpen && columnId) {
      console.log("user_details:", JSON.stringify(user_details, null, 2));
      console.log("Received columnId:", columnId);
      console.log("Computed validStatus:", validStatus);
    }
  }, [isOpen, columnId, user_details, validStatus]);

  // Component state
  const [formData, setFormData] = useState({
    ...initialFormData,
    status: validStatus,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  
  // API hooks
  const [createTodo, { isLoading: isCreatingTodo }] = useCreateTodoMutation();
  const { data: usersList, isLoading: usersLoading, error: usersError } = useGetUsersQuery({
    page,
    page_size: 20,
    ordering: "-id",
    search: debouncedSearchTerm,
  });

  // Handle users data loading
  useEffect(() => {
    if (usersList?.data?.results) {
      console.log("usersList received:", usersList.data.results.length, "users");
      setAllUsers((prev) => (page === 1 ? usersList.data.results : [...prev, ...usersList.data.results]));
    }
    if (usersError) {
      setError("Failed to load users. Please try again.");
    }
  }, [usersList, usersError, page]);

  // Reset page when search term changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm]);

  // Process users data
  const users = useMemo(
    () =>
      Array.isArray(allUsers)
        ? allUsers.map((user: any) => ({
            id: user?.id || 0,
            name: user?.fullnames || `${user?.first_name ?? ""} ${user?.last_name ?? ""}`.trim() || "Unknown",
            email: user?.email || "",
            employee_no: user?.employee_no || "",
          }))
        : [],
    [allUsers]
  );

  // Filter users based on search term
  const filteredUsers = useMemo(() => {
    if (!debouncedSearchTerm.trim()) return users;
    const searchLower = debouncedSearchTerm.toLowerCase();
    return users.filter(
      (user) => 
        user.name.toLowerCase().includes(searchLower) || 
        user.email.toLowerCase().includes(searchLower)
    );
  }, [users, debouncedSearchTerm]);

  // Validate assignees when users list changes
  useEffect(() => {
    if (formData.assignees.length > 0) {
      const validAssignees = formData.assignees.filter((id) => 
        users.some((u) => u.id.toString() === id)
      );
      if (validAssignees.length !== formData.assignees.length) {
        updateFormData({ assignees: validAssignees });
      }
    }
  }, [users, formData.assignees]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsSubmitting(false);
      setFormData({ ...initialFormData, status: validStatus });
      setSearchTerm("");
      setError(null);
    }
  }, [isOpen, validStatus]);

  // Helper function to update form data
  const updateFormData = (updates: Partial<typeof formData>) => {
    setFormData((prev) => ({ ...prev, ...updates }));
  };

  // Toggle assignee selection (single selection only)
  const toggleAssignee = (userId: string) => {
    const newAssignees = formData.assignees.includes(userId)
      ? formData.assignees.filter((id) => id !== userId)
      : [userId]; // Only allow single selection
    updateFormData({ assignees: newAssignees });
  };

  // Form validation function
  const validateForm = () => {
    const { title, priority, assignees, client_type, prospect, customer, sale, dueDate, dueTime, status } = formData;

    if (!title.trim()) return "Title is required";
    if (!priority) return "Priority is required";
    if (!assignees.length) return "An assignee is required";
    if (!VALID_STATUSES.includes(status)) return `Status must be one of: ${VALID_STATUSES.join(", ")}`;
    
    // Validate client-specific fields
    if (client_type === "Prospect" && (!prospect || prospect === 0)) {
      return "Prospect ID is required when client type is Prospect";
    }
    if (client_type === "Customer" && !customer.trim()) {
      return "Customer is required when client type is Customer";
    }
    if (client_type === "Sale" && !sale.trim()) {
      return "Sale is required when client type is Sale";
    }
    
    // Validate date/time formats
    if (dueDate && !/^\d{4}-\d{2}-\d{2}$/.test(dueDate)) {
      return "Due Date must be in YYYY-MM-DD format";
    }
    if (dueTime && !/^\d{2}:\d{2}$/.test(dueTime)) {
      return "Due Time must be in hh:mm format";
    }
    
    // Validate user details
    if (!user_details?.employee_no || !user_details?.fullnames || !user_details?.email) {
      return "Authenticated user details are incomplete. Please log in again or contact support.";
    }
    
    return null;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    console.log("📝 handleSubmit called at:", new Date().toISOString());
    console.log("🔍 isCreatingTodo:", isCreatingTodo);
    console.log("🔍 isSubmitting:", isSubmitting);

    // Prevent double submission
    if (isCreatingTodo || isSubmitting) {
      console.log("🛑 Submission blocked - already creating todo");
      return;
    }

    setIsSubmitting(true);

    // Check if users are still loading
    if (usersLoading) {
      setError("Users are still loading. Please wait.");
      setIsSubmitting(false);
      return;
    }

    // Validate form
    const validationError = validateForm();
    if (validationError) {
      console.log("Validation failed:", validationError);
      setError(validationError);
      setIsSubmitting(false);
      return;
    }

    // Check if users are available
    if (!users.length) {
      setError("No users available. Please wait for users to load or try again.");
      setIsSubmitting(false);
      return;
    }

    // Validate assigned user
    const assignedUser = users.find((u) => u.id.toString() === formData.assignees[0]);
    if (!assignedUser) {
      console.log("Available users:", JSON.stringify(users, null, 2));
      console.log("Selected assignee ID:", formData.assignees[0]);
      setError("Selected assignee not found. Please select a valid assignee.");
      setIsSubmitting(false);
      return;
    }

    // Validate assigned user details
    if (assignedUser.id === 0 || !assignedUser.employee_no) {
      console.log("Invalid assigned user:", JSON.stringify(assignedUser, null, 2));
      setError("Selected user has an invalid ID or missing employee number. Please select a valid user.");
      setIsSubmitting(false);
      return;
    }

    // Validate current user details
    if (!user_details.employee_no) {
      console.log("Invalid user_details:", JSON.stringify(user_details, null, 2));
      setError("Authenticated user has no employee number. Please log in again or contact support.");
      setIsSubmitting(false);
      return;
    }

    // Create reminder time if set_reminder is true and we have due date/time
    let reminder_time = null;
    if (formData.set_reminder && formData.dueDate) {
      const dueDateTime = formData.dueTime 
        ? `${formData.dueDate}T${formData.dueTime}:00.000Z`
        : `${formData.dueDate}T00:00:00.000Z`;
      reminder_time = dueDateTime;
    }

    // Build the payload according to the required structure
    const payload: any = {
      title: formData.title,
      description: formData.description || "",
      due_date: formData.dueDate || null,
      due_time: formData.dueTime || null,
      status: VALID_STATUSES.includes(formData.status) ? formData.status : "PENDING",
      priority: formData.priority,
      set_reminder: formData.set_reminder,
      reminder_time: reminder_time,
      client_type: formData.client_type === "none" ? null : formData.client_type,
      created_by: user_details.employee_no, // Use employee_no for created_by
    };

    // Add client-specific fields only if they have values
    if (formData.client_type === "Customer" && formData.customer) {
      payload.customer = formData.customer;
    }
    if (formData.client_type === "Prospect" && formData.prospect && formData.prospect !== 0) {
      payload.prospect = formData.prospect;
    }
    if (formData.client_type === "Sale" && formData.sale) {
      payload.sale = formData.sale;
    }

    console.log("🚀 Submitting payload:", JSON.stringify(payload, null, 2));

    // Retry logic for API calls
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts) {
      try {
        const result = await Promise.race([
          createTodo(payload).unwrap(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error("Request timed out")), 10000)
          ),
        ]);
        
        console.log("✅ Todo created successfully:", JSON.stringify(result, null, 2));
        
        // Reset form and close modal on success
        setFormData({ ...initialFormData, status: validStatus });
        setSearchTerm("");
        setIsSubmitting(false);
        onClose();
        return;
        
      } catch (err: any) {
        attempts++;
        console.error(`Create todo error (attempt ${attempts}):`, {
          status: err.status,
          data: JSON.stringify(err.data, null, 2),
          originalStatus: err.originalStatus,
          error: err.error,
          message: err.message,
          rawResponse: err.data?.toString().substring(0, 1000),
        });
        
        if (attempts === maxAttempts) {
          const errorMessage =
            err.message === "Request timed out"
              ? "Request timed out. Please try again."
              : err.data?.detail ||
                err.data?.non_field_errors?.join(", ") ||
                Object.entries(err.data || {})
                  .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(", ") : value}`)
                  .join("; ") ||
                `Failed to create todo: ${err.error || "Server error. Please check your inputs or contact support."}`;
          setError(errorMessage);
          setIsSubmitting(false);
        }
      }
    }
  };

  // Early return for authentication issues
  if (!user_details?.employee_no || !user_details?.fullnames || !user_details?.email) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
        <div className="bg-white rounded-2xl p-6 max-w-md">
          <h2 className="text-xl font-bold text-red-600 mb-4">Authentication Required</h2>
          <p className="text-sm text-gray-700 mb-4">
            Please log in to create a todo. If this persists, contact support.
          </p>
          <button onClick={onClose} className="px-4 py-2 bg-blue-600 text-white rounded-lg">
            Close
          </button>
        </div>
      </div>
    );
  }

  // Early return for users loading error
  if (usersError) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
        <div className="bg-white rounded-2xl p-6 max-w-md">
          <h2 className="text-xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-sm text-gray-700 mb-4">Failed to load users. Please try again.</p>
          <button onClick={onClose} className="px-4 py-2 bg-blue-600 text-white rounded-lg">
            Close
          </button>
        </div>
      </div>
    );
  }

  // Main modal render
  return (
    <AnimatePresence>
      {isOpen && columnId && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            onClick={onClose}
            className="absolute inset-0 bg-black backdrop-blur-sm"
          />
          
          {/* Modal Content */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
          >
            {/* Loading Overlay */}
            {(isCreatingTodo || isSubmitting) && (
              <div className="absolute inset-0 bg-white/50 flex items-center justify-center z-50">
                <div className="flex flex-col items-center gap-2">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <div className="text-sm text-gray-600">
                    {isSubmitting ? "Submitting..." : "Creating todo..."}
                  </div>
                </div>
              </div>
            )}
            
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Plus className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Add New Card</h2>
                  <p className="text-sm text-gray-500">
                    to list <span className="font-medium">{columnTitle || "Unknown"}</span>
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                disabled={isCreatingTodo}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            {/* Modal Form */}
            <form
              onSubmit={(e) => {
                console.log("Form submitted with formData:", JSON.stringify(formData, null, 2));
                handleSubmit(e);
              }}
              className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]"
            >
              {/* Error Display */}
              {error && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg text-sm">
                  {error}
                </div>
              )}
              
              {/* Title Field */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Card Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => updateFormData({ title: e.target.value })}
                  placeholder="Enter a title for this card..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                  required
                  disabled={isCreatingTodo}
                />
              </div>

              {/* Description Field */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => updateFormData({ description: e.target.value })}
                  placeholder="Add a more detailed description..."
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                  rows={4}
                  disabled={isCreatingTodo}
                />
              </div>

              {/* Priority Selection */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-900 mb-3">
                  Priority *
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {PRIORITY_OPTIONS.map((option) => {
                    const Icon = option.icon;
                    const isSelected = formData.priority === option.value;
                    return (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => updateFormData({ priority: option.value })}
                        className={`flex items-center gap-3 p-3 rounded-lg border-2 transition-all duration-200 ${
                          isSelected 
                            ? "border-blue-500 bg-blue-50" 
                            : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                        } disabled:opacity-50`}
                        disabled={isCreatingTodo}
                      >
                        <div className={`p-2 rounded-lg bg-gradient-to-r ${option.color}`}>
                          <Icon className="h-4 w-4 text-white" />
                        </div>
                        <span className={`font-medium ${isSelected ? "text-blue-900" : "text-gray-700"}`}>
                          {option.label}
                        </span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Due Date and Time */}
              <div className="mb-6 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Due Date
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="date"
                      value={formData.dueDate}
                      onChange={(e) => updateFormData({ dueDate: e.target.value })}
                      className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                      disabled={isCreatingTodo}
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Due Time
                  </label>
                  <input
                    type="time"
                    value={formData.dueTime}
                    onChange={(e) => updateFormData({ dueTime: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                    disabled={isCreatingTodo}
                  />
                </div>
              </div>

              {/* Reminder Checkbox */}
              <div className="mb-6">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.set_reminder}
                    onChange={(e) => updateFormData({ set_reminder: e.target.checked })}
                    className="form-checkbox text-blue-600 rounded disabled:opacity-50"
                    disabled={isCreatingTodo}
                  />
                  <span className="text-sm font-semibold text-gray-900">Set Reminder</span>
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Reminder will be set for the due date and time if enabled
                </p>
              </div>

              {/* Assignee Selection */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-900 mb-3">
                  Assign Member *
                </label>
                
                {/* Search Input */}
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search members..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                    disabled={isCreatingTodo}
                  />
                  {searchTerm && (
                    <button
                      type="button"
                      onClick={() => setSearchTerm("")}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center disabled:opacity-50"
                      disabled={isCreatingTodo}
                    >
                      <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </button>
                  )}
                </div>
                
                {/* Users List */}
                <div className="border border-gray-200 rounded-lg p-2 bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                    {usersLoading ? (
                      <div className="col-span-full text-center py-8 text-gray-500">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
                        Loading users...
                      </div>
                    ) : filteredUsers.length === 0 ? (
                      <div className="col-span-full text-center py-8 text-gray-500">
                        No users found
                      </div>
                    ) : (
                      filteredUsers.map((member) => {
                        const isSelected = formData.assignees.includes(member.id.toString());
                        return (
                          <button
                            key={member.id}
                            type="button"
                            onClick={() => {
                              console.log("Selecting user:", JSON.stringify(member, null, 2));
                              toggleAssignee(member.id.toString());
                            }}
                            className={`flex items-center gap-3 p-3 rounded-lg border-2 transition-all duration-200 bg-white ${
                              isSelected 
                                ? "border-blue-500 bg-blue-50" 
                                : "border-gray-200 hover:border-gray-300"
                            } disabled:opacity-50`}
                            disabled={isCreatingTodo}
                          >
                            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-xs font-semibold">
                              {member.name.split(" ").map((n: string) => n[0]).join("")}
                            </div>
                            <div className="text-left min-w-0 flex-1">
                              <div className={`text-sm font-medium truncate ${
                                isSelected ? "text-blue-900" : "text-gray-900"
                              }`}>
                                {member.name}
                              </div>
                              <div className="text-xs text-gray-500 truncate">
                                {member.email}
                              </div>
                            </div>
                            {isSelected && <CheckSquare className="h-4 w-4 text-blue-500" />}
                          </button>
                        );
                      })
                    )}
                  </div>
                </div>
              </div>

              {/* Client Type Selection */}
              <div className="mb-6">
                <label className="block text-sm font-semibold text-gray-900 mb-3">
                  Client Type
                </label>
                <div className="flex flex-wrap gap-4">
                  {CLIENT_TYPE_OPTIONS.map((type) => (
                    <label key={type.value} className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="client_type"
                        value={type.value}
                        checked={formData.client_type === type.value}
                        onChange={() => updateFormData({ 
                          client_type: type.value,
                          // Reset client-specific fields when changing type
                          customer: type.value === "Customer" ? formData.customer : "",
                          prospect: type.value === "Prospect" ? formData.prospect : 0,
                          sale: type.value === "Sale" ? formData.sale : "",
                        })}
                        className="form-radio text-blue-600 disabled:opacity-50"
                        disabled={isCreatingTodo}
                      />
                      <span className="text-sm text-gray-700">{type.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Client-Specific Fields */}
              {formData.client_type === "Customer" && (
                <div className="mb-6">
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Customer *
                  </label>
                  <input
                    type="text"
                    placeholder="Customer identifier"
                    value={formData.customer}
                    onChange={(e) => updateFormData({ customer: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                    required
                    disabled={isCreatingTodo}
                  />
                </div>
              )}

              {formData.client_type === "Prospect" && (
                <div className="mb-6">
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Prospect ID *
                  </label>
                  <input
                    type="number"
                    placeholder="Prospect ID"
                    value={formData.prospect || ""}
                    onChange={(e) => updateFormData({ prospect: Number(e.target.value) || 0 })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                    required
                    disabled={isCreatingTodo}
                  />
                </div>
              )}

              {formData.client_type === "Sale" && (
                <div className="mb-6">
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Sale *
                  </label>
                  <input
                    type="text"
                    placeholder="Sale identifier"
                    value={formData.sale}
                    onChange={(e) => updateFormData({ sale: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                    required
                    disabled={isCreatingTodo}
                  />
                </div>
              )}

              {/* Form Actions */}
              <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                  disabled={isCreatingTodo}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={
                    isCreatingTodo || 
                    isSubmitting || 
                    !formData.title.trim() || 
                    !formData.assignees.length ||
                    (formData.client_type === "Customer" && !formData.customer.trim()) ||
                    (formData.client_type === "Prospect" && (!formData.prospect || formData.prospect === 0)) ||
                    (formData.client_type === "Sale" && !formData.sale.trim())
                  }
                  className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white text-sm font-medium rounded-lg transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  {(isCreatingTodo || isSubmitting) ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Adding...
                    </>
                  ) : (
                    "Add Card"
                  )}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};