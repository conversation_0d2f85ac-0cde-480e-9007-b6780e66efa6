import { useState, useEffect, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { Calendar, Plus, RefreshCw, ChevronRight } from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import AddEventModal from "./AddReview";
import EditEventModal from "./EditReview";
import CalendarView from "./CalenderView";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Toggle } from "@/components/ui/toggle";
import { format, parse } from "date-fns";
import {
  useGetRemindersQuery,
  useAddReminderMutation,
  useUpdateReminderMutation,
  useDeleteReminderMutation,
  useSnoozeReminderMutation,
} from "@/redux/slices/reminderApiSlice";

interface Reminder {
  reminder_id?: string;
  reminder_type: 'Follow-up Call' | 'Payment Reminder' | 'Document Collection' | 'Site Visit' | 'Meeting' | 'Email' | 'SMS' | 'General';
  reminder_date?: string;
  reminder_time?: string;
  priority?: 'Low' | 'Normal' | 'High' | 'Urgent';
  reminder_notes?: string;
  action_url?: string;
  
  // API field names
  title?: string;
  description?: string;
  
  // Optional fields
  status?: 'Active' | 'Snoozed' | 'Completed' | 'Cancelled';
  client_type?: 'Prospect' | 'Customer' | 'Sale' | null;
  created_at?: string;
  created_by?: string;
  customer?: string | null;
  prospect?: number | string | null;
  sale?: string | null;
}

const convertReminderToEvent = (reminder: Reminder) => {
  const reminderDateTime = reminder.reminder_date && reminder.reminder_time
    ? parse(`${reminder.reminder_date} ${reminder.reminder_time}`, 'yyyy-MM-dd HH:mm:ss', new Date())
    : new Date();

  return {
    id: reminder.reminder_id || '',
    date: reminderDateTime,
    reminder_title: reminder.title || '',
    reminder_description: reminder.description || 'No description',
    reminder_time: reminder.reminder_time || format(reminderDateTime, 'HH:mm:ss'),
    reminder_date: reminder.reminder_date || format(reminderDateTime, 'yyyy-MM-dd'),
    status: reminder.status || 'Active',
    priority: reminder.priority || 'Normal',
    reminder_type: reminder.reminder_type || 'General',
    reminder_notes: reminder.reminder_notes || '',
    action_url: reminder.action_url || '',
    client_type: reminder.client_type || null, // Preserve null
    created_by: reminder.created_by || '',
    customer: reminder.customer || null,
    prospect: reminder.prospect || null,
    sale: reminder.sale || null,
  };
};

const PriorityBadge = ({ priority }: { priority: string }) => {
  const priorityMap = {
    Low: 'bg-blue-100 text-blue-800',
    Normal: 'bg-green-100 text-green-800',
    High: 'bg-yellow-100 text-yellow-800',
    Urgent: 'bg-red-100 text-red-800',
  };

  return (
    <Badge className={`${priorityMap[priority as keyof typeof priorityMap] || 'bg-gray-100 text-gray-800'} text-xs font-medium`}>
      {priority}
    </Badge>
  );
};

export default function EventsReminder() {
  const [date, setDate] = useState<Date>(new Date());
  const [selectedDayEvents, setSelectedDayEvents] = useState<ReturnType<typeof convertReminderToEvent>[]>([]);
  const [newEvent, setNewEvent] = useState({
    title: "",
    description: "",
    reminder_time: "",
    reminder_date: format(new Date(), 'yyyy-MM-dd'),
    reminder_type: "General" as Reminder['reminder_type'],
    priority: "Normal" as Reminder['priority'],
    status: "Active" as Reminder['status'],
    reminder_notes: "",
    action_url: "",
    client_type: null as Reminder['client_type'], // Initialize as null
    created_by: "current_user_id", // Should be set from auth context
    customer: null as string | null,
    prospect: null as number | null,
    sale: null as string | null,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [eventToEdit, setEventToEdit] = useState<ReturnType<typeof convertReminderToEvent> | null>(null);
  const [showEvents, setShowEvents] = useState(false);
  const [showAllEvents, setShowAllEvents] = useState(false);
  const { toast } = useToast();

  const {
    data: remindersResponse,
    error: remindersError,
    isLoading: remindersLoading,
    refetch: refetchReminders,
  } = useGetRemindersQuery({});

  const userPermissions = useMemo(() => {
    return [
      "Book Visit",
      "Complete Trips",
      "Create Vehicle Request",
      "Create Special Assignment",
      "Access Logistics Dashboard",
      "Access Logistics Statistics",
      "VIEW_SALES_OWN_MARKETER",
      "VIEW_CUSTOMER_OWN_MARKETER",
      "VIEW_PROSPECT_ALL_OFFICES",
      "VIEW_INVENTORY_MARKETER",
    ];
  }, []);

  const hasPermission = (permission: string) => userPermissions.includes(permission);

  const remindersData = useMemo(() => {
    if (!remindersResponse) return [];
    
    if (remindersResponse.data && Array.isArray(remindersResponse.data.results)) {
      return remindersResponse.data.results;
    }
    
    if (remindersResponse.data && Array.isArray(remindersResponse.data)) {
      return remindersResponse.data;
    }
    
    if (Array.isArray(remindersResponse)) {
      return remindersResponse;
    }
    
    if (remindersResponse.results && Array.isArray(remindersResponse.results)) {
      return remindersResponse.results;
    }
    
    return [];
  }, [remindersResponse]);

  const [addReminder] = useAddReminderMutation();
  const [updateReminder] = useUpdateReminderMutation();
  const [deleteReminder] = useDeleteReminderMutation();
  const [snoozeReminder] = useSnoozeReminderMutation();

  const events = useMemo(() => {
    if (!Array.isArray(remindersData)) return [];
    return remindersData.map(reminder => convertReminderToEvent(reminder));
  }, [remindersData]);

  const hasEvents = (day: Date) => {
    return events.some(
      (event) =>
        event.date.getDate() === day.getDate() &&
        event.date.getMonth() === day.getMonth() &&
        event.date.getFullYear() === day.getFullYear()
    );
  };

  useEffect(() => {
    const dayEvents = showAllEvents
      ? events
      : events.filter(
          (event) =>
            event.date.getDate() === date.getDate() &&
            event.date.getMonth() === date.getMonth() &&
            event.date.getFullYear() === date.getFullYear()
        );

    setSelectedDayEvents(dayEvents);
    setShowEvents(dayEvents.length > 0);
  }, [date, events, showAllEvents]);

  useEffect(() => {
    setNewEvent(prev => ({
      ...prev,
      reminder_date: format(date, 'yyyy-MM-dd')
    }));
  }, [date]);

  const handleAddEvent = async () => {
    if (!hasPermission("VIEW_PROSPECT_ALL_OFFICES")) {
      toast({
        title: "Permission Denied",
        description: "You do not have permission to add reminders.",
        variant: "destructive",
      });
      return;
    }

    if (!newEvent.title) {
      toast({
        title: "Missing information",
        description: "Please provide at least a title for the reminder.",
        variant: "destructive",
      });
      return;
    }

    try {
      const reminderData = {
        reminder_type: newEvent.reminder_type,
        reminder_date: newEvent.reminder_date,
        reminder_time: newEvent.reminder_time ? `${newEvent.reminder_time}:00` : "00:00:00",
        priority: newEvent.priority,
        reminder_notes: newEvent.reminder_notes || null,
        action_url: newEvent.action_url || null,
        title: newEvent.title,
        description: newEvent.description || null,
        status: newEvent.status,
        client_type: newEvent.client_type, // Can be null
        created_by: newEvent.created_by,
        customer: newEvent.customer,
        prospect: newEvent.prospect,
        sale: newEvent.sale,
      };

      await addReminder(reminderData).unwrap();

      setNewEvent({
        title: "",
        description: "",
        reminder_time: "",
        reminder_date: format(new Date(), 'yyyy-MM-dd'),
        reminder_type: "General",
        priority: "Normal",
        status: "Active",
        reminder_notes: "",
        action_url: "",
        client_type: null,
        created_by: "current_user_id",
        customer: null,
        prospect: null,
        sale: null,
      });
      setIsModalOpen(false);

      toast({
        title: "Reminder added",
        description: "Your reminder has been successfully added.",
      });

      refetchReminders();
    } catch (error) {
      toast({
        title: "Error adding reminder",
        description: "There was an error adding your reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveEvent = async (eventId: string) => {
    if (!hasPermission("VIEW_PROSPECT_ALL_OFFICES")) {
      toast({
        title: "Permission Denied",
        description: "You do not have permission to delete reminders.",
        variant: "destructive",
      });
      return;
    }

    try {
      await deleteReminder(eventId).unwrap();
      setSelectedDayEvents(selectedDayEvents.filter((event) => event.id !== eventId));

      toast({
        title: "Reminder removed",
        description: "Your reminder has been successfully removed.",
      });

      refetchReminders();
    } catch (error) {
      toast({
        title: "Error removing reminder",
        description: "There was an error removing your reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditEvent = (event: ReturnType<typeof convertReminderToEvent>) => {
    if (!hasPermission("VIEW_PROSPECT_ALL_OFFICES")) {
      toast({
        title: "Permission Denied",
        description: "You do not have permission to edit reminders.",
        variant: "destructive",
      });
      return;
    }
    setEventToEdit(event);
    setIsEditModalOpen(true);
  };

  const handleSaveEvent = async () => {
    if (!eventToEdit) return;

    try {
      const reminderData = {
        reminder_type: eventToEdit.reminder_type,
        reminder_date: eventToEdit.reminder_date,
        reminder_time: eventToEdit.reminder_time,
        priority: eventToEdit.priority,
        reminder_notes: eventToEdit.reminder_notes || null,
        action_url: eventToEdit.action_url || null,
        title: eventToEdit.reminder_title,
        description: eventToEdit.reminder_description || null,
        status: eventToEdit.status,
        client_type: eventToEdit.client_type, // Can be null
        created_by: eventToEdit.created_by,
        customer: eventToEdit.customer,
        prospect: eventToEdit.prospect,
        sale: eventToEdit.sale,
      };

      await updateReminder({
        id: eventToEdit.id,
        ...reminderData,
      }).unwrap();

      setIsEditModalOpen(false);
      setEventToEdit(null);

      toast({
        title: "Reminder updated",
        description: "Your reminder has been successfully updated.",
      });

      refetchReminders();
    } catch (error) {
      toast({
        title: "Error updating reminder",
        description: "There was an error updating your reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSnoozeEvent = async (eventId: string, snoozeUntil: Date) => {
    try {
      await snoozeReminder({
        id: eventId,
        reminder_date: format(snoozeUntil, 'yyyy-MM-dd'),
        reminder_time: format(snoozeUntil, 'HH:mm:ss'),
        status: 'Snoozed',
      }).unwrap();

      toast({
        title: "Reminder snoozed",
        description: "Your reminder has been snoozed successfully.",
      });

      refetchReminders();
    } catch (error) {
      toast({
        title: "Error snoozing reminder",
        description: "There was an error snoozing your reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (remindersLoading) {
    return (
      <Screen>
        <div className="flex flex-col h-full">
          <div className="px-6 py-4 border-b">
            <div className="flex items-center gap-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-4 w-60" />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
          <div className="flex flex-1 overflow-hidden">
            <div className="flex-1 p-4">
              <Skeleton className="h-full w-full rounded-xl" />
            </div>
            <div className="w-80 border-l p-4">
              <Skeleton className="h-full w-full rounded-lg" />
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  if (remindersError) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-full">
          <div className="text-center max-w-md p-6 bg-white dark:bg-slate-900 rounded-xl shadow-lg">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 mb-4">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
              Failed to load reminders
            </h3>
            <p className="text-slate-600 dark:text-slate-400 mb-4">
              {remindersError.toString()}
            </p>
            <Button onClick={() => refetchReminders()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen className="bg-slate-50 dark:bg-slate-950">
      <header className="flex-none px-6 py-4 border-b bg-white dark:bg-slate-900 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-300">
              <Calendar className="h-5 w-5" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Reminders</h1>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {format(date, 'MMMM yyyy')} • {events.length} total reminders
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Toggle
              pressed={showAllEvents}
              onPressedChange={setShowAllEvents}
              className="data-[state=on]:bg-purple-100 data-[state=on]:text-purple-700 dark:data-[state=on]:bg-purple-900/50 dark:data-[state=on]:text-purple-300"
            >
              {showAllEvents ? "Show Today" : "Show All"}
            </Toggle>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchReminders()}
              className="gap-1.5"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
            
            
          </div>
        </div>
      </header>

      <div className="flex flex-1 min-h-0 overflow-hidden">
        <div className="flex-1 p-4 min-h-0 overflow-auto">
          <div className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-800 h-full">
            <CalendarView
              date={date}
              setDate={setDate}
              hasEvents={hasEvents}
              showEvents={showEvents}
              setShowEvents={setShowEvents}
            />
          </div>
        </div>
        
        <div className="w-96 border-l bg-white dark:bg-slate-900 flex flex-col">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h2 className="font-semibold text-lg">
                {showAllEvents ? "All Reminders" : "Today's Reminders"}
              </h2>
              <Badge variant="outline" className="px-2 py-1 text-xs">
                {selectedDayEvents.length} items
              </Badge>
            </div>
            <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
              {format(date, 'EEEE, MMMM d')}
            </p>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {selectedDayEvents.length > 0 ? (
              selectedDayEvents.map((event) => (
                <div 
                  key={event.id}
                  className="p-3 rounded-lg border border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors cursor-pointer"
                  onClick={() => handleEditEvent(event)}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-medium">{event.reminder_title}</h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                        {format(parse(event.reminder_time, 'HH:mm:ss', new Date()), 'HH:mm')} • {event.reminder_description}
                      </p>
                      {event.reminder_notes && (
                        <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                          Notes: {event.reminder_notes}
                        </p>
                      )}
                      {event.client_type && (
                        <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                          Client: {event.client_type}
                        </p>
                      )}
                    </div>
                    <ChevronRight className="h-5 w-5 text-slate-400" />
                  </div>
                  <div className="flex gap-2 mt-3">
                    <PriorityBadge priority={event.priority} />
                    {event.reminder_type !== 'General' && (
                      <Badge variant="outline" className="text-xs">
                        {event.reminder_type}
                      </Badge>
                    )}
                    {event.status === 'Snoozed' && (
                      <Badge variant="secondary" className="text-xs">
                        Snoozed
                      </Badge>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center p-8">
                <Calendar className="h-10 w-10 text-slate-300 dark:text-slate-600 mb-4" />
                <h3 className="font-medium text-slate-500 dark:text-slate-400 mb-1">
                  No reminders found
                </h3>
                <p className="text-sm text-slate-400 dark:text-slate-500 mb-4">
                  {showAllEvents 
                    ? "You don't have any reminders yet." 
                    : "You don't have any reminders for today."}
                </p>
                
              </div>
            )}
          </div>
        </div>
      </div>

      
      
      
    </Screen>
  );
}