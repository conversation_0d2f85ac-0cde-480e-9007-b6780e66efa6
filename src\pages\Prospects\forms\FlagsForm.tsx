import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  useCreateFlagMutation,
  usePartialUpdateFlagMutation,
} from "@/redux/slices/flagsApiSlice";

type Props = {
  prospect_id?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const FlagsForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [addFlag, { isLoading: submitting }] = useCreateFlagMutation();
  const [updateFlag, { isLoading: updating }] = usePartialUpdateFlagMutation();
  // form schema
  const formSchema = z.object({
    flag_reason: z.string().min(1, { message: "Flag Type is required." }),
    description: z.string().min(1, { message: "description is required." }),
    follow_up_required: z.boolean().default(false),
    follow_up_date: z.string().optional(),
    set_reminder: z.boolean().default(false),
    reminder_time: z.string().optional(),
    resolution_date: z.string().optional(),
    resolution_note: z.string().optional(),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      flag_reason: updateData?.flag_reason || "",
      description: updateData?.description || "",
    },
  });

  const handleFlagSubmit = async (data: any) => {
    // Convert datetime-local to ISO format and remove empty fields
    const processedData = { ...data };

    if (processedData.reminder_time) {
      processedData.reminder_time = new Date(
        processedData.reminder_time
      ).toISOString();
    } else {
      delete processedData.reminder_time; // Remove empty field
    }

    if (processedData.follow_up_date) {
      // Convert date to datetime with time set to start of day
      processedData.follow_up_date = new Date(
        processedData.follow_up_date + "T00:00:00"
      ).toISOString();
    } else {
      delete processedData.follow_up_date; // Remove empty field
    }

    if (processedData.resolution_date) {
      // Convert date to datetime with time set to start of day
      processedData.resolution_date = new Date(
        processedData.resolution_date + "T00:00:00"
      ).toISOString();
    } else {
      delete processedData.resolution_date; // Remove empty field
    }

    let formData = updateData
      ? { ...processedData, flag_id: updateData?.flag_id }
      : { ...processedData, client_type: "Prospect", prospect: prospect_id };
    try {
      let res;
      if (updateData) {
        res = await updateFlag(formData).unwrap();
      } else {
        res = await addFlag(formData).unwrap();
      }
      if (res?.flag_id) {
        toast.success(`Flag ${updateData ? "updated" : "added"} successfully`);
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Somethe went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleFlagSubmit)}>
        <div className="space-y-2 pb-20">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Flag Reason*
            </label>
            <div className="relative pt-1">
              <select
                {...register("flag_reason")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Follow-up Required">Follow-up Required</option>
                <option value="Payment Issue">Payment Issue</option>
                <option value="Customer Complaint">Customer Complaint</option>
                <option value="Documentation Missing">
                  Documentation Missing
                </option>
                <option value="Quality Issue">Quality Issue</option>
                <option value="Process Violation">Process Violation</option>
                <option value="Other">Other</option>
              </select>
            </div>
            {errors.flag_reason && (
              <p className="text-red-500 text-xs pl-3">
                {errors.flag_reason.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Descriptions*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("description")}
                rows={3}
                placeholder="Description"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.description && (
              <p className="text-red-500 text-xs pl-3">
                {errors.description.message}
              </p>
            )}
          </div>

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("follow_up_required")} />
              </div>
              <label className="ml-1 text-sm">Follow Up Required</label>
              {errors.follow_up_required && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.follow_up_required.message}
                </p>
              )}
            </div>
          </div>

          {watch().follow_up_required && (
            <div>
              <label className="ml-1 text-sm">
                Follow Up Date{" "}
                {updateData?.follow_up_date
                  ? `(Current set on: ${new Date(
                      updateData.follow_up_date
                    ).toLocaleDateString()})`
                  : ""}
              </label>
              <div className="relative pt-1">
                <input
                  type="date"
                  {...register("follow_up_date")}
                  placeholder="Your Email"
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
              {errors.follow_up_date && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.follow_up_date.message}
                </p>
              )}
            </div>
          )}

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("set_reminder")} />
              </div>
              <label className="ml-1 text-sm">Set Reminder</label>
              {errors.set_reminder && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.set_reminder.message}
                </p>
              )}
            </div>
          </div>

          {watch().set_reminder && (
            <div>
              <label className="ml-1 text-sm">
                Reminder Date{" "}
                {updateData?.reminder_time
                  ? `(Currently set at: ${new Date(
                      updateData.reminder_time
                    ).toLocaleDateString()})`
                  : ""}
              </label>
              <div className="relative pt-1">
                <input
                  type="datetime-local"
                  {...register("reminder_time")}
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
              {errors.reminder_time && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.reminder_time.message}
                </p>
              )}
            </div>
          )}

          <div>
            <label className="ml-1 text-sm">
              Resolution Date{" "}
              {updateData?.resolution_date
                ? `(Current set on: ${new Date(
                    updateData.resolution_date
                  ).toLocaleDateString()})`
                : ""}
            </label>
            <div className="relative pt-1">
              <input
                type="date"
                {...register("resolution_date")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.resolution_date && (
              <p className="text-red-500 text-xs pl-3">
                {errors.resolution_date.message}
              </p>
            )}
          </div>

          {watch().resolution_date && (
            <div>
              <label htmlFor="" className="ml-1 text-sm">
                Resolution Note
              </label>
              <div className="relative pt-1">
                <textarea
                  {...register("resolution_note")}
                  rows={3}
                  placeholder="Resolution Note"
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                ></textarea>
              </div>
              {errors.resolution_note && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.resolution_note.message}
                </p>
              )}
            </div>
          )}

          <div className="w-full">
            {submitting || updating ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default FlagsForm;
