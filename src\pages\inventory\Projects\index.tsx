import { Screen } from "@/app-components/layout/screen";
import {
  <PERSON><PERSON>utton,
  OutlinedButton,
} from "@/components/custom/buttons/buttons";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card6 } from "@/components/custom/cards/Card6";
import { Form, FormControl, FormItem } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { addComma, addCommaNoDecimal } from "@/utils/helpers";
import {
  DollarSignIcon,
  EyeOff,
  LayoutDashboardIcon,
  MapIcon,
  ReceiptText,
  TrendingUp,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Link } from "react-router-dom";
import {
  useGetDashboardStatsQuery,
  useGetProjectsQuery,
} from "@/redux/slices/projects";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { projectTypes } from "@/types/project";
import BookingsModal from "@/pages/inventory/Projects/modals/BookingsModal";
import DiaporaBookingModal from "./modals/DiaporaBookingModal";
import airplane from "@/assets/airplane.png";
import DiaporaReservationModal from "./modals/DiaporaReservationModal";
import { Card, CardContent } from "@/components/ui/card";
import ForexModal from "./modals/ForexModal";
import ProjectManagementModal from "./modals/ProjectManagementModal";
import MapsModal from "./modals/MapsModal";
import PlotPaymentOptionModal from "./modals/PlotPaymentOptionModal";
import { useSidebarPermissions } from "@/hooks/useSidebarPermissions";

type Props = {};

const index = ({}: Props) => {
  // scroll to top of page on reload
  useEffect(() => {
    window.scroll(0, 0);
  }, []);

  const [isBookingsOpen, setIsBookingsOpen] = useState(false);
  const [isLinksOpen, setIsLinksOpen] = useState(false);

  // Get inventory permissions
  const { hasInventoryPermission } = useSidebarPermissions();
  const hasFullAccess = hasInventoryPermission("VIEW_INVENTORY_FULL_ACCESS");
  const hasMarketerPermission = hasInventoryPermission(
    "VIEW_INVENTORY_MARKETER"
  );
  const hasDiasporaPermission = hasInventoryPermission(
    "VIEW_INVENTORY_DIASPORA"
  );
  const hasMapsPermission = hasInventoryPermission("VIEW_INVENTORY_MAPS");

  // modals state definitions
  const [sbModal, setSbModal] = useState(false);
  const [mpModal, setMpModal] = useState(false);
  const [obModal, setObModal] = useState(false);
  const [dbModal, setDbModal] = useState(false);
  const [fXModal, setFXModal] = useState(false);
  const [dtrModal, setdtrModal] = useState(false);
  const [projectsModal, setProjectModal] = useState(false);
  const [openMapModal, setOpenMapModal] = useState(false);
  const [plotPaymentOption, setPlotPaymentOption] = useState(false);

  const [filter, setFilter] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const { data: projects, isLoading: projectsLoading } = useGetProjectsQuery({
    search: searchValue,
    page: currentPage,
    page_size: itemsPerPage,
    visibiliy: "SHOW",
    ordering: filter,
  });
  console.log("projectssssssss", projects);
  const { data: stats, isLoading: statsLoading } = useGetDashboardStatsQuery(
    {}
  );

  const metrics = [
    {
      title: "All Projects",
      value: addCommaNoDecimal(stats?.projects_count ?? "0"),
      description: "Total number of projects",
      icon: LayoutDashboardIcon,
      iconBg:
        "bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900",
      iconColor: "text-blue-600 dark:text-blue-300",
      change: "Projects",
      positive: true,
      cardBg:
        "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800",
      borderColor: "border-blue-200 dark:border-blue-700",
    },
    {
      title: "Total Units",
      value: addCommaNoDecimal(stats?.plots_count ?? "0"),
      description: "Total number of units across all projects",
      icon: LayoutDashboardIcon,
      iconBg:
        "bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-800 dark:to-purple-900",
      iconColor: "text-purple-600 dark:text-purple-300",
      change: "Increasing",
      positive: true,
      cardBg:
        "bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800",
      borderColor: "border-purple-200 dark:border-purple-700",
    },
    {
      title: "Total Open Units",
      value: addCommaNoDecimal(stats?.open_plots_count ?? "0"),
      description: "Units available for sale across all projects",
      icon: LayoutDashboardIcon,
      iconBg:
        "bg-gradient-to-br from-green-100 to-green-200 dark:from-green-800 dark:to-green-900",
      iconColor: "text-green-600 dark:text-green-300",
      change: "Increasing",
      positive: true,
      cardBg:
        "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800",
      borderColor: "border-green-200 dark:border-green-700",
    },
    {
      title: "Total Sold Units",
      value: addCommaNoDecimal(stats?.sold_plots_count ?? "0"),
      description: "Units sold across all projects",
      icon: LayoutDashboardIcon,
      iconBg:
        "bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-800 dark:to-orange-900",
      iconColor: "text-orange-600 dark:text-orange-300",
      change: "Increasing",
      positive: true,
      cardBg:
        "bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900 dark:to-orange-800",
      borderColor: "border-orange-200 dark:border-orange-700",
    },
  ];

  return (
    <Screen>
      <div className="min-h-screen space-y-5">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold ml-4">Optiven Projects</h1>
        </div>
        <div className=" grid lg:grid-cols-4 sm:grid-cols-1 px-4 gap-4">
          <div className="lg:col-span-3 sm:col-span-1">
            {statsLoading ? (
              <div className="w-full flex items-center justify-start">
                <SpinnerTemp type="spinner-double" size="sm" />
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-2 ">
                {metrics.map((m) => (
                  <Card
                    key={m.title}
                    className={`border-2 ${m.borderColor} shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 ${m.cardBg}`}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {m.title}
                          </p>
                          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                            {m.value}
                          </p>
                          <p className="hidden lg:flex text-xs text-gray-600 dark:text-gray-400">
                            {m.description}
                          </p>
                        </div>
                        <div
                          className={`w-12 h-12 rounded-xl flex items-center justify-center ${m.iconBg} shadow-md`}
                        >
                          <m.icon className={`w-6 h-6 ${m.iconColor}`} />
                        </div>
                      </div>
                      <div className="mt-4 flex items-center">
                        <TrendingUp
                          className={`w-4 h-4 mr-1 ${
                            m.positive ? "text-green-600" : "text-red-500"
                          }`}
                        />
                        <span
                          className={`text-sm font-medium ${
                            m.positive
                              ? "text-green-700 dark:text-green-400"
                              : "text-red-600 dark:text-red-400"
                          }`}
                        >
                          {m.change}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
            <p className="mt-5 font-bold flex items-center pb-2">
              My Booking Status{" "}
            </p>
            {statsLoading ? (
              <div className="w-full flex items-center justify-start">
                <SpinnerTemp type="spinner-double" size="sm" />
              </div>
            ) : (
              <div className="grid  grid-cols-3 gap-2 pb-3">
                <div className=" p-4 border border-primary/10 rounded-md bg-gradient-to-br from-green-400/50 to-gray-50 dark:from-green-400/20 dark:to-white/0 dark:border-0">
                  <p className="font-bold text-xl">
                    {addCommaNoDecimal(stats?.special_bookings_count ?? "0")}
                  </p>
                  <p className="text-xs">My Special Bookings</p>
                </div>
                <div className=" p-4 border border-primary/10 rounded-md bg-gradient-to-br from-green-400/50 to-gray-50 dark:from-green-400/20 dark:to-white/0 dark:border-0">
                  <p className="font-bold text-xl">
                    {addCommaNoDecimal(stats?.mpesa_bookings_count ?? "0")}
                  </p>
                  <p className="text-xs">My M-Pesa Bookings</p>
                </div>
                <div className=" p-4 border border-primary/10 rounded-md bg-gradient-to-br from-green-400/50 to-gray-50 dark:from-green-400/20 dark:to-white/0 dark:border-0">
                  <p className="font-bold text-xl">
                    {addCommaNoDecimal(stats?.other_bookings_count ?? "0")}
                  </p>
                  <p className="text-xs">My Other Bookings</p>
                </div>
              </div>
            )}
            {(hasFullAccess || hasDiasporaPermission) && (
              <div className="shadow bg-primary rounded dark:border dark:border-white/10 p-4 my-3 flex items-end justify-between relative">
                <p className="my-3 mx-2 font-bold text-secondary dark:!text-white">
                  {" "}
                  Diapora Trips Plot Reservations
                </p>
                <div className="flex flex-wrap gap-2 items-center">
                  <Link to="/diaspora-reservations">
                    <OutlinedButton
                      variant="outline"
                      className="dark:bg-black/10 dark:border-white/40 text-white/80"
                    >
                      View
                    </OutlinedButton>
                  </Link>
                  <OutlinedButton
                    variant="outline"
                    className="dark:bg-black/10 dark:border-white/40 text-white/80"
                    onClick={() => setdtrModal(true)}
                  >
                    Reserve
                  </OutlinedButton>
                  <OutlinedButton
                    variant="outline"
                    className="dark:bg-black/10 dark:border-white/40 text-white/80"
                    onClick={() => setDbModal(true)}
                  >
                    Receipt Booking
                  </OutlinedButton>
                </div>
                <img
                  src={airplane}
                  alt=""
                  className="md:w-60 w-32 absolute md:-top-15 -top-10 right-[60%] md:right-[45%]"
                />
              </div>
            )}
            <div className="shadow rounded border dark:border-white/10 p-4 my-3">
              <p className="my-3 mx-2 font-bold"> Filter Projects</p>
              <Form {...useForm()}>
                <form className="grid md:grid-cols-5 sm:grid-cols-1 gap-3">
                  <FormItem className="md:col-span-4 sm:col-span-1 ">
                    <FormControl>
                      <Input
                        type="text"
                        name="search"
                        value={searchValue}
                        onChange={(e) => setSearchValue(e.target.value)}
                        placeholder="Search for Projects by Name"
                        className="dark:bg-transparent dark:border-white/10"
                      />
                    </FormControl>
                  </FormItem>
                  <FormItem>
                    <Select value={filter} onValueChange={(e) => setFilter(e)}>
                      <SelectTrigger className="dark:bg-transparent dark:border-white/10">
                        <SelectValue placeholder="Filter By" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Select</SelectLabel>
                          <SelectItem value="name">
                            Alphabetical Ascending
                          </SelectItem>
                          <SelectItem value="-name">
                            Alphabetical Descending
                          </SelectItem>
                          <SelectItem value="priority">
                            Priority Ascending
                          </SelectItem>
                          <SelectItem value="-priority">
                            Priority Descending
                          </SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </FormItem>
                </form>
              </Form>
            </div>
          </div>

          <div className="col-span-1 h-full">
            <div className="space-y-4 sticky top-4">
              {/* Plot Bookings Section */}
              <div className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700">
                <div className="bg-gradient-to-r from-emerald-500 to-green-600 px-5 py-3 rounded-t-lg">
                  <div className="flex justify-between items-center">
                    <h3 className="text-white font-bold text-lg tracking-wide">
                      Plot Bookings
                    </h3>
                    <button
                      onClick={() => setIsBookingsOpen(!isBookingsOpen)}
                      className="md:hidden"
                    >
                      {isBookingsOpen ? (
                        <ChevronUp className="text-white" />
                      ) : (
                        <ChevronDown className="text-white" />
                      )}
                    </button>
                  </div>
                </div>
                <div
                  className={`${
                    isBookingsOpen ? "block" : "hidden"
                  } sm:block transition-all duration-300`}
                >
                  <div className="p-4 gap-2 grid md:grid-cols-2 grid-cols-1">
                    {[
                      {
                        text: "Special Bookings",
                        onClick: () => setSbModal(true),
                        allowedForMarketer: true,
                        allowedForDiaspora: false,
                        // icon: ReceiptText,
                      },
                      {
                        text: "M-Pesa Bookings",
                        onClick: () => setMpModal(true),
                        allowedForMarketer: true,
                        allowedForDiaspora: false,
                        // icon: ReceiptText,
                      },
                      {
                        text: "Other Bookings",
                        onClick: () => setObModal(true),
                        allowedForMarketer: true,
                        allowedForDiaspora: false,
                        // icon: ReceiptText,
                      },
                      {
                        text: "Reserve Plot",
                        onClick: () => setdtrModal(true),
                        allowedForMarketer: false,
                        allowedForDiaspora: true,
                        requiresDiaspora: true,
                        // icon: ReceiptText,
                      },
                      {
                        text: "Diaspora Receipting",
                        onClick: () => setDbModal(true),
                        allowedForMarketer: false,
                        allowedForDiaspora: true,
                        requiresDiaspora: true,
                        // icon: ReceiptText,
                      },
                      {
                        text: "Plot Payment Options",
                        onClick: () => setPlotPaymentOption(true),
                        allowedForMarketer: true,
                        allowedForDiaspora: true,
                        requiresDiaspora: false,
                        // icon: ReceiptText,
                      },
                    ]
                      .filter((item) => {
                        // If user has full access, show all buttons
                        if (hasFullAccess) {
                          return true;
                        }

                        // Check if button requires diaspora permission
                        if (item.requiresDiaspora && !hasDiasporaPermission) {
                          return false;
                        }

                        // If user has marketer permission, show only marketer-allowed buttons
                        if (hasMarketerPermission && !hasDiasporaPermission) {
                          return item.allowedForMarketer;
                        }

                        // If user has diaspora permission, show only diaspora-allowed buttons
                        if (hasDiasporaPermission && !hasMarketerPermission) {
                          return item.allowedForDiaspora;
                        }

                        // If user has both marketer and diaspora permissions, show buttons allowed for either
                        if (hasMarketerPermission && hasDiasporaPermission) {
                          return (
                            item.allowedForMarketer || item.allowedForDiaspora
                          );
                        }

                        // If user doesn't have marketer or diaspora permission, show all buttons (assuming they have other permissions)
                        return true;
                      })
                      .map((item, index) => (
                        <button
                          key={index}
                          onClick={item.onClick}
                          className="w-full flex items-center !justify-start gap-3 px-4 py-3 rounded-xl text-start text-gray-700 dark:text-gray-200 hover:bg-accent/700 dark:hover:bg-gray-700/50 transition-all duration-200 group bg-accent"
                        >
                          {/* <item.icon
                          size={18}
                          className="group-hover:text-emerald-500 transition-colors"
                        /> */}
                          <span className="text-sm font-medium group-hover:text-emerald-500 transition-colors">
                            {item.text}
                          </span>
                        </button>
                      ))}
                  </div>
                </div>
              </div>

              {/* Quick Links Section */}
              {(hasFullAccess || hasMapsPermission) && (
                <div className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-xl border border-gray-100 dark:border-gray-700">
                  <div className="bg-gradient-to-r from-blue-500 to-indigo-600 py-3 px-5 rounded-t-lg">
                    <div className="flex justify-between items-center">
                      <h3 className="text-white font-bold text-lg tracking-wide">
                        Quick Links
                      </h3>
                      <button
                        onClick={() => setIsLinksOpen(!isLinksOpen)}
                        className="md:hidden"
                      >
                        {isLinksOpen ? (
                          <ChevronUp className="text-white" />
                        ) : (
                          <ChevronDown className="text-white" />
                        )}
                      </button>
                    </div>
                  </div>
                  <div
                    className={`${
                      isLinksOpen ? "block" : "hidden"
                    } sm:block transition-all duration-300`}
                  >
                    <div className="p-4 space-y-2 ">
                      {[
                        {
                          text: "Manage Project Visibility",
                          onClick: () => setProjectModal(true),
                          icon: EyeOff,
                        },
                        {
                          text: "Map Management",
                          onClick: () => setOpenMapModal(true),
                          icon: MapIcon,
                        },
                        {
                          text: "Forex Management",
                          onClick: () => setFXModal(true),
                          icon: DollarSignIcon,
                        },
                      ].map((item, index) => (
                        <button
                          key={index}
                          onClick={item.onClick}
                          className="w-full flex text-start items-center gap-3 px-4 py-3 rounded-xl text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-200 group"
                        >
                          <item.icon
                            size={18}
                            className="group-hover:text-blue-500 transition-colors"
                          />
                          <span className="text-sm font-medium group-hover:text-blue-500 transition-colors">
                            {item.text}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* projects list */}

        <div className="px-4">
          <p className="mx-2 my-2 font-bold">OPTIVEN PROJECTS</p>

          <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1  gap-4">
            {projectsLoading ? (
              <SpinnerTemp type="spinner-double" size="md" />
            ) : (
              projects?.map((project: projectTypes) => (
                <Link
                  key={project?.projectId}
                  to={`/projects/${project?.projectId}`}
                  className="rounded border p-4 bg-gradient-to-b from-primary/20 to-white dark:to-white/0 "
                >
                  <div className="flex justify-center">
                    <div className="">
                      <p className="font-bold text-center">{project?.name}</p>
                      <p className="text-sm text-primary text-center">
                        {project?.open_plots} Plots Available{" "}
                        {(hasFullAccess ||
                          (!hasMarketerPermission &&
                            !hasDiasporaPermission)) && (
                          <span className="text-destructive ">
                            {addComma(project?.percentage_sold ?? "0")}% Sold
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center justify-center my-2 w-full">
                    {(hasMarketerPermission || hasDiasporaPermission) &&
                    !hasFullAccess ? (
                      // Show only Open plots for marketer or diaspora users (unless they have full access)
                      <div className="rounded flex text-sm w-full justify-center">
                        <p className="px-2 py-1 bg-primary text-white text-center w-full">
                          {project?.open_plots} Open Plots
                        </p>
                      </div>
                    ) : (
                      // Show all plot statuses for full access users or other users
                      <div className="rounded flex text-sm w-full justify-center">
                        <p className="px-2 py-1 bg-primary text-white text-center w-[30%]">
                          {project?.open_plots} Open
                        </p>
                        <p className="px-2 py-1 bg-destructive text-white text-center w-[30%]">
                          {project?.sold_plots} Sold
                        </p>
                        <p className="px-2 py-1 bg-yellow-400  text-center w-[30%]">
                          {project?.reserved_plots} RVD
                        </p>
                      </div>
                    )}
                  </div>
                </Link>
              ))
            )}
          </div>
        </div>

        <BookingsModal
          sbModal={sbModal}
          setSbModal={setSbModal}
          bookingType="SPECIAL"
          title="Special Booking"
        />
        <BookingsModal
          sbModal={mpModal}
          setSbModal={setMpModal}
          bookingType="MPESA"
          title="Mpesa Booking"
        />
        <BookingsModal
          sbModal={obModal}
          setSbModal={setObModal}
          bookingType="OTHER"
          title="Other Booking"
        />
        <DiaporaBookingModal
          sbModal={dbModal}
          setSbModal={setDbModal}
          bookingType="DIASPORA"
          title="Diaspora Plot Reciepting"
        />
        <DiaporaReservationModal
          openModal={dtrModal}
          setOpenModal={setdtrModal}
          title="Diaspora Trip Plot Reservations"
        />

        <ProjectManagementModal
          // data={projects?.data?.results}
          openModal={projectsModal}
          setOpenModal={setProjectModal}
          totalItems={projects?.data?.total_data || 0}
          setSearchValue={setSearchValue}
          searchValue={searchValue}
        />
        <ForexModal openModal={fXModal} setOpenModal={setFXModal} />
        <MapsModal
          data={projects?.data}
          openModal={openMapModal}
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          setOpenModal={setOpenMapModal}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
        />

        <PlotPaymentOptionModal
          openModal={plotPaymentOption}
          setOpenModal={setPlotPaymentOption}
        />
      </div>
    </Screen>
  );
};

export default index;
